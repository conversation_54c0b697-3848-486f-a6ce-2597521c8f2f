# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*
!.env.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

*storybook.log

*.vscode
.vscode/*
.idea/*

# Playwright
node_modules/
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

# Vitest
vitest-report/index.html
vitest-report/hooks/index.html
vitest-report/hooks/use-mobile.tsx.html
vitest-report/lib/format-date.ts.html
vitest-report/lib/format-number.ts.html
vitest-report/lib/index.html
vitest-report/stores/index.html
vitest-report/stores/private-model-text-store.ts.html
