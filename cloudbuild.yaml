steps:
  #Step 1: Build container image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      # Next public Backend URL
      - '--build-arg'
      - 'NEXT_PUBLIC_GOLANG_SERVER_URL=$_NEXT_PUBLIC_GOLANG_SERVER_URL'
      # Next public Front URL
      - '--build-arg'
      - 'NEXTAUTH_URL=$_AUTH_URL'
      - '-t'
      - 'europe-west3-docker.pkg.dev/$PROJECT_ID/frontend-repository-kwore/front-v2'
      - '--build-arg'
      - 'ENV=$_ENV' # Change 'dev' to the desired environment (dev, pd, ppd)
      - '.'

  # Step 2: Push the container image to Artifact Registry
  - name: 'gcr.io/cloud-builders/docker'
    args:
      [
        'push',
        'europe-west3-docker.pkg.dev/$PROJECT_ID/frontend-repository-kwore/front-v2',
      ]

  # Step 3: Deploy to Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'front-v2'
      - '--image'
      - 'europe-west3-docker.pkg.dev/$PROJECT_ID/frontend-repository-kwore/front-v2'
      - '--region'
      - 'europe-west1'
      - '--port'
      - '3000'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
