import { useTranslations } from 'next-intl';
import React from 'react';

import { PostSuggestion } from '@/api/models/dtos/post.dto';

import { cn } from '@/lib/utils';

import { creativePostFormOpts } from '@/config/creative-post/form.config';

import { ExpandableCard } from '@/components/shared/expandable-card';
import { withForm } from '@/components/shared/form';
import { Icons } from '@/components/shared/icons';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

export const GeneralInfos = withForm({
  ...creativePostFormOpts,
  render: function Render({ form }) {
    const t = useTranslations();

    return (
      <ExpandableCard
        title={t('creative_card.form.general_infos.title')}
        description={t('creative_card.form.general_infos.description')}
        required
        initialOpen={true}
      >
        <div className='grid gap-3'>
          <form.AppField name='SocialMedia'>
            {(field) => (
              <Label htmlFor={field.name} className='space-y-2'>
                <div>
                  <span>
                    {t('creative_card.form.general_infos.social_media')}
                  </span>
                  <span className='ms-0.5 text-destructive'>*</span>
                </div>
                <RadioGroup
                  className='grid grid-cols-4 place-items-center'
                  id={field.name}
                  value={field.state.value}
                  onValueChange={(value) => {
                    field.handleChange(value as PostSuggestion['SocialMedia']);
                  }}
                >
                  <div
                    className={cn(
                      'shadow-xs relative flex w-full cursor-pointer flex-col items-center gap-3 rounded-md border border-input px-0 py-1.5 text-center outline-none',
                      {
                        'bg-primary/20': field.state.value === 'facebook',
                      }
                    )}
                  >
                    <RadioGroupItem
                      id='facebook'
                      value='facebook'
                      className='sr-only'
                    />
                    <label
                      htmlFor='facebook'
                      className='cursor-pointer after:absolute after:inset-0'
                    >
                      <Icons.facebook className='size-5 text-primary' />
                    </label>
                  </div>

                  <div
                    className={cn(
                      'shadow-xs relative flex w-full cursor-pointer flex-col items-center gap-3 rounded-md border border-input px-0 py-1.5 text-center outline-none',
                      {
                        'bg-rose/20': field.state.value === 'instagram',
                      }
                    )}
                  >
                    <RadioGroupItem
                      id='instagram'
                      value='instagram'
                      className='sr-only'
                    />
                    <label
                      htmlFor='instagram'
                      className='cursor-pointer after:absolute after:inset-0'
                    >
                      <Icons.instagram className='size-5 text-rose' />
                    </label>
                  </div>

                  <div
                    className={cn(
                      'shadow-xs relative flex w-full cursor-pointer flex-col items-center gap-3 rounded-md border border-input px-0 py-1.5 text-center outline-none',
                      {
                        'bg-black/20': field.state.value === 'x',
                      }
                    )}
                  >
                    <RadioGroupItem id='x' value='x' className='sr-only' />

                    <label
                      htmlFor='x'
                      className='cursor-pointer after:absolute after:inset-0'
                    >
                      <Icons.x className='text-dark size-5' />
                    </label>
                  </div>

                  <div
                    className={cn(
                      'shadow-xs relative flex w-full cursor-pointer flex-col items-center gap-3 rounded-md border border-input px-0 py-1.5 text-center outline-none',
                      {
                        'bg-sky-200': field.state.value === 'linkedin',
                      }
                    )}
                  >
                    <RadioGroupItem
                      id='linkedin'
                      value='linkedin'
                      className='sr-only'
                    />
                    <label
                      htmlFor='linkedin'
                      className='cursor-pointer text-xs font-medium leading-none text-foreground after:absolute after:inset-0'
                    >
                      <Icons.linkedin className='size-5 text-sky-500' />
                    </label>
                  </div>
                </RadioGroup>
              </Label>
            )}
          </form.AppField>
          <form.AppField name='Context'>
            {(field) => (
              <field.TextField
                type='text'
                label={t('creative_card.form.general_infos.fields.title.label')}
                placeholder={t(
                  'creative_card.form.general_infos.fields.title.placeholder'
                )}
              />
            )}
          </form.AppField>
          <form.AppField name='Project'>
            {(field) => (
              <field.TextareaField
                label={t(
                  'creative_card.form.general_infos.fields.description.label'
                )}
                placeholder={t(
                  'creative_card.form.general_infos.fields.title.label'
                )}
              />
            )}
          </form.AppField>
        </div>
      </ExpandableCard>
    );
  },
});
