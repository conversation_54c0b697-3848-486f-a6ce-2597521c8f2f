'use client';

import { useTranslations } from 'next-intl';
import React from 'react';

import { getFilteredPrivateModelsByModelType } from '@/lib/helpers/creative/get-filtered-private-models-by-model-type';

import { useCreativePostOptions } from '@/hooks/creative/use-creative-post-options';

import { useAllByCompanyBrandId } from '@/api/hooks/ai/queries';
import { useAllKnowledge } from '@/api/hooks/knowledge/queries';

import { updatePostFormOpts } from '@/config/creative-post/form.config';

import { withForm } from '@/components/shared/form';
import MultipleSelector from '@/components/shared/multiselect';
import { Label } from '@/components/ui/label';

export const UpdatePostForm = withForm({
  defaultValues: {
    ...updatePostFormOpts.defaultValues,
  },
  render: function Render({ form }) {
    const t = useTranslations();

    const { ctaTexts, marketingStrategies, toneOfVoices, languages } =
      useCreativePostOptions();

    const { data: privateModels, isFetching: isFetchingPrivateModels } =
      useAllByCompanyBrandId();
    const { data: knowledges } = useAllKnowledge();

    return (
      <form
        className='grid gap-3'
        onSubmit={async (e) => {
          e.preventDefault();
          await form.handleSubmit();
        }}
      >
        <div className='grid gap-3'>
          <form.AppField name='Context'>
            {(field) => (
              <field.TextField
                type='text'
                label={t('edit_text_creative_card.update_post_form.title')}
                placeholder={t(
                  'edit_text_creative_card.update_post_form.placeholder'
                )}
              />
            )}
          </form.AppField>
          <form.AppField name='Project'>
            {(field) => (
              <field.TextareaField
                label={t(
                  'edit_text_creative_card.update_post_form.description'
                )}
                placeholder={t(
                  'edit_text_creative_card.update_post_form.placeholder'
                )}
              />
            )}
          </form.AppField>
        </div>
        <form.AppField name='CallToAction'>
          {() => (
            <div className='grid grid-cols-2 gap-2'>
              <form.AppField name={'CallToAction.CTAtype'}>
                {(subField) => (
                  <subField.SelectField
                    label={t(
                      'edit_text_creative_card.update_post_form.call_to_action'
                    )}
                    required={false}
                    placeholder={t(
                      'edit_text_creative_card.update_post_form.placeholder'
                    )}
                    options={ctaTexts}
                  />
                )}
              </form.AppField>
              <form.AppField
                name='CallToAction.CTALink'
                listeners={{
                  onChange: ({ value }) => {
                    form.setFieldValue(
                      'CallToAction.WithCallToAction',
                      (value && value.length > 0) || false
                    );
                  },
                }}
              >
                {(subField) => (
                  <subField.TextField
                    required={false}
                    label={t('edit_text_creative_card.update_post_form.link')}
                    placeholder={t(
                      'edit_text_creative_card.update_post_form.placeholder'
                    )}
                  />
                )}
              </form.AppField>
            </div>
          )}
        </form.AppField>
        <form.AppField name='MarketingStrategy'>
          {(field) => (
            <field.SelectField
              required={false}
              label={t(
                'edit_text_creative_card.update_post_form.marketing_strategy'
              )}
              placeholder={t(
                'edit_text_creative_card.update_post_form.placeholder'
              )}
              options={marketingStrategies}
            />
          )}
        </form.AppField>
        <form.AppField name='TextOptions'>
          {() => (
            <>
              <form.AppField name={'TextOptions.PrivateModelId'}>
                {(subField) => (
                  <subField.SelectField
                    label={t(
                      'edit_text_creative_card.update_post_form.private_model'
                    )}
                    required={false}
                    placeholder={t(
                      'edit_text_creative_card.update_post_form.placeholder'
                    )}
                    isLoading={isFetchingPrivateModels}
                    options={getFilteredPrivateModelsByModelType({
                      modelType: 'LLM',
                      privateModelsDataList: privateModels,
                    })}
                  />
                )}
              </form.AppField>
              <form.AppField name={'TextOptions.Knowledges'}>
                {(field) => {
                  const selectedOptions = field.state.value
                    ? knowledges
                        ?.filter((k) => field.state.value?.includes(k.Id))
                        .map((k) => ({
                          label: k.Name,
                          value: k.Id,
                        }))
                    : [];

                  return (
                    <>
                      <Label>
                        {t(
                          'edit_text_creative_card.update_post_form.knowledge'
                        )}
                      </Label>
                      <MultipleSelector
                        options={
                          knowledges?.map((knowledge) => ({
                            label: knowledge.Name,
                            value: knowledge.Id,
                          })) ?? []
                        }
                        value={selectedOptions}
                        onChange={(selectedOptions) => {
                          field.handleChange(
                            selectedOptions.map((option) => option.value)
                          );
                        }}
                        placeholder={t(
                          'edit_text_creative_card.update_post_form.placeholder'
                        )}
                        emptyIndicator={
                          <p className='text-center text-sm'>
                            No results found
                          </p>
                        }
                      />
                    </>
                  );
                }}
              </form.AppField>
            </>
          )}
        </form.AppField>
        <div className='grid grid-cols-2 gap-2'>
          <form.AppField name='Ad.language'>
            {(field) => (
              <field.SelectField
                label={t('edit_text_creative_card.update_post_form.language')}
                required={false}
                placeholder={t(
                  'edit_text_creative_card.update_post_form.placeholder'
                )}
                options={languages}
              />
            )}
          </form.AppField>
          <form.AppField name='TextOptions.ToneOfVoice'>
            {(field) => (
              <field.SelectField
                required={false}
                label={t(
                  'edit_text_creative_card.update_post_form.tone_of_voice'
                )}
                placeholder={t(
                  'edit_text_creative_card.update_post_form.placeholder'
                )}
                options={toneOfVoices}
              />
            )}
          </form.AppField>
        </div>
      </form>
    );
  },
});
