import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react';

import { cn } from '@/lib/utils';

import { useAllCampaigns } from '@/api/hooks/campaign/queries';
import { useSaveSuggestionsById } from '@/api/hooks/posts/mutations';
import { useCreativePostStore } from '@/stores/creative-post-store';

import { BaseSelect } from '@/components/shared/base-select';
import { DatePickerSelect } from '@/components/shared/date-picker-select';
import { Icons } from '@/components/shared/icons';
import { TimeZoneSelect } from '@/components/shared/time-zone-select';
import { Button } from '@/components/ui/button';
import { CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogTitle,
  DialogClose,
  DialogHeader,
  DialogFooter,
  DialogContent,
  DialogTrigger,
  DialogDescription,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';

// Note: this file will be refactored once the schedule and publish features is implemented
export function PostSubmissionControls() {
  const [open, setOpen] = useState(false);
  const t = useTranslations();

  const { getCurrentPostSuggestion } = useCreativePostStore();
  const currentPostSuggestion = getCurrentPostSuggestion();

  const { data: campaignsData, isFetching: isFetchingCampaigns } =
    useAllCampaigns();
  const { mutateAsync: saveSuggestionsById, isSuccess: isSaveSuccess } =
    useSaveSuggestionsById();

  useEffect(() => {
    if (isSaveSuccess) {
      setOpen(false);
    }
  }, [isSaveSuccess]);

  return (
    <CardContent className='flex flex-col gap-2 p-3'>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <Button
            variant='outline'
            size='sm'
            className={cn(
              'bg-[#006FEE]/20 text-[#3776DC] hover:bg-[#006FEE]/30 hover:text-[#3776DC]',
              {
                'border-[#3776DC] bg-background': !currentPostSuggestion,
              }
            )}
            iconStart={<Icons.bookmark strokeWidth={0.5} />}
            disabled={!currentPostSuggestion}
          >
            {t('save')}
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {t('post_submission_controls.dialog_save.title')}
            </DialogTitle>
            <DialogDescription>
              {t('post_submission_controls.dialog_save.description')}
            </DialogDescription>
          </DialogHeader>
          <BaseSelect
            label={t('post_submission_controls.link_with_campaign.label')}
            required={false}
            placeholder={t(
              'post_submission_controls.link_with_campaign.placeholder'
            )}
            isLoading={isFetchingCampaigns}
            options={
              campaignsData?.map((campaign) => ({
                value: campaign.Id,
                label: campaign.Name,
              })) ?? []
            }
          />
          <DialogFooter className='border-t pt-2'>
            <DialogClose asChild>
              <Button variant='destructive'>{t('cancel')}</Button>
            </DialogClose>
            <Button
              onClick={async () => {
                await saveSuggestionsById({
                  postId: currentPostSuggestion?.Id ?? '',
                });
              }}
            >
              {t('save')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog>
        <DialogTrigger asChild>
          <Button
            size='sm'
            className={cn(
              'bg-[#006FEE]/20 text-[#3776DC] hover:bg-[#006FEE]/30',
              {
                'bg-[#006FEE33]/20': !currentPostSuggestion,
              }
            )}
            iconStart={<Icons.calendarAdd />}
            disabled={!currentPostSuggestion}
          >
            {t('post_submission_controls.dialog_schedule.title')}
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {' '}
              {t('post_submission_controls.dialog_schedule.title')}
            </DialogTitle>
            <DialogDescription>
              {t('post_submission_controls.dialog_schedule.description')}
            </DialogDescription>
          </DialogHeader>
          <div className='grid grid-cols-2 gap-2'>
            <DatePickerSelect />
            <TimeZoneSelect />
          </div>
          <div className='space-y-1'>
            <Label>
              {t('post_submission_controls.dialog_schedule.social_media.label')}
            </Label>
            <div className='flex items-center gap-2'>
              <BaseSelect
                required={false}
                showOptionIcon
                showOptionLabel={false}
                placeholder={t(
                  'post_submission_controls.dialog_schedule.social_media.select_platform'
                )}
                options={socialMediaOptions}
              />
              <BaseSelect
                wrapperClassName='flex-1'
                required={false}
                placeholder={t(
                  'post_submission_controls.dialog_schedule.social_media.page_name'
                )}
                options={[]}
              />
            </div>
          </div>

          <BaseSelect
            label={t('post_submission_controls.link_with_campaign.label')}
            required={false}
            placeholder={t(
              'post_submission_controls.link_with_campaign.placeholder'
            )}
            isLoading={isFetchingCampaigns}
            options={
              campaignsData?.map((campaign) => ({
                value: campaign.Id,
                label: campaign.Name,
              })) ?? []
            }
          />
          <DialogFooter className='border-t pt-2'>
            <DialogClose asChild>
              <Button variant='destructive'>{t('cancel')}</Button>
            </DialogClose>
            <Button>{t('save')}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog>
        <DialogTrigger asChild>
          <Button
            size='sm'
            className='bg-[#162F58]'
            iconStart={<Icons.cloudUpload />}
            disabled={!currentPostSuggestion}
          >
            {t('post_submission_controls.dialog_publish.title')}
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {t('post_submission_controls.dialog_publish.title')}
            </DialogTitle>
            <DialogDescription>
              {t('post_submission_controls.dialog_publish.description')}{' '}
            </DialogDescription>
          </DialogHeader>
          <BaseSelect
            label={t(
              'post_submission_controls.dialog_publish.social_media.label'
            )}
            required={false}
            placeholder={t(
              'post_submission_controls.dialog_publish.social_media.page_name'
            )}
            options={[]}
          />
          <BaseSelect
            label={t('post_submission_controls.link_with_campaign.label')}
            required={false}
            placeholder={t(
              'post_submission_controls.link_with_campaign.placeholder'
            )}
            isLoading={isFetchingCampaigns}
            options={
              campaignsData?.map((campaign) => ({
                value: campaign.Id,
                label: campaign.Name,
              })) ?? []
            }
          />
          <DialogFooter className='border-t pt-2'>
            <DialogClose asChild>
              <Button variant='destructive'>{t('cancel')}</Button>
            </DialogClose>
            <Button>
              {t('post_submission_controls.dialog_publish.title')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </CardContent>
  );
}

const socialMediaOptions = [
  {
    label: 'platform.facebook',
    value: 'facebook',
    icon: <Icons.facebook className='size-5 text-primary' />,
  },
  {
    label: 'platform.instagram',
    value: 'instagram',
    icon: <Icons.instagram className='size-5 text-rose' />,
  },
  {
    label: 'platform.linkedin',
    value: 'linkedin',
    icon: <Icons.linkedin className='size-5 text-sky-500' />,
  },
  {
    label: 'platform.twitter',
    value: 'x',
    icon: <Icons.x className='size-5 text-black' />,
  },
];
