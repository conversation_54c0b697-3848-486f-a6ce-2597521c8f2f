import { X } from 'lucide-react';

import { cn } from '@/lib/utils';

import { useRouter } from '@/i18n/routing';
import { useCreativePostStore } from '@/stores/creative-post-store';

import { Icons } from '@/components/shared/icons';
import { Button } from '@/components/ui/button';
import { CardContent } from '@/components/ui/card';

export const PostEditTextControls = () => {
  const router = useRouter();

  const selectedIndex = useCreativePostStore((s) => s.selectedIndex);
  const currentPostSuggestion = useCreativePostStore(
    (s) => s.currentPostSuggestion
  );
  const editPostSuggestion = useCreativePostStore((s) => s.editPostSuggestion);
  const postSuggestions = useCreativePostStore((s) => s.postSuggestions);

  const setCurrentPostSuggestion = useCreativePostStore(
    (s) => s.setCurrentPostSuggestion
  );
  const setPostSuggestions = useCreativePostStore((s) => s.setPostSuggestions);
  const setEditPostSuggestion = useCreativePostStore(
    (s) => s.setEditPostSuggestion
  );

  function handleSave() {
    if (editPostSuggestion && currentPostSuggestion && postSuggestions) {
      const updated = {
        ...currentPostSuggestion,
        Ad: editPostSuggestion.Ad,
        Score: {
          ...editPostSuggestion.Score,
          CreationTimestamp: currentPostSuggestion.Score.CreationTimestamp,
        },
        CreationTimestamp: currentPostSuggestion.CreationTimestamp,
      };

      setCurrentPostSuggestion(updated);

      const updatedSuggestions = {
        ...postSuggestions,
        results: postSuggestions.results.map((item, index) =>
          index === selectedIndex ? updated : item
        ),
      };

      setPostSuggestions(updatedSuggestions);
      setEditPostSuggestion(null);
      router.back();
    }
  }

  return (
    <CardContent className='flex flex-col gap-2 p-3'>
      <Button
        variant='outline'
        size='sm'
        className={cn(
          'bg-[#006FEE]/20 text-[#3776DC] hover:bg-[#006FEE]/30 hover:text-[#3776DC]'
        )}
        disabled={!editPostSuggestion}
        iconStart={<Icons.bookmark strokeWidth={0.5} />}
        onClick={handleSave}
      >
        Save
      </Button>
      <Button
        variant='destructive'
        className='w-full'
        size='sm'
        iconStart={<X />}
        onClick={() => {
          router.back();
          setEditPostSuggestion(null);
        }}
      >
        Cancel
      </Button>
    </CardContent>
  );
};
