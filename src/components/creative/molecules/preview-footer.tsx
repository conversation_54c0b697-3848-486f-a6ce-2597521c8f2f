import { useTranslations } from 'next-intl';

import { useRouter } from '@/i18n/routing';
import { useCreativePostStore } from '@/stores/creative-post-store';

import { Icons } from '@/components/shared/icons';
import { Button } from '@/components/ui/button';

export function PreviewFooter() {
  const router = useRouter();
  const t = useTranslations();

  const { clearCurrentPostSuggestion, clearPostSuggestions } =
    useCreativePostStore();

  const currentPostSuggestion = useCreativePostStore(
    (s) => s.currentPostSuggestion
  );

  return (
    <div className='overflow-hidden rounded-lg border border-gray-200 bg-gray-50/50'>
      <Button
        variant='ghost'
        className='rounded-none border-r border-gray-200 px-4 py-2 text-gray-500 hover:bg-gray-100 hover:text-gray-700'
        onClick={() => {
          clearPostSuggestions();
          clearCurrentPostSuggestion();
        }}
        iconStart={<Icons.refreshSquare />}
        disabled={!currentPostSuggestion}
      >
        {t('post_suggestion_preview.buttons.reset')}
      </Button>
      <Button
        variant='ghost'
        className='rounded-none border-r border-gray-200 px-4 py-2 text-gray-500 hover:bg-gray-100 hover:text-gray-700'
        iconStart={<Icons.penNew />}
        disabled={!currentPostSuggestion}
        onClick={() => {
          router.push('/dashboard/creative/edit-text');
        }}
      >
        {t('post_suggestion_preview.buttons.edit_text')}
      </Button>
      <Button
        variant='ghost'
        className='rounded-none px-4 py-2 text-gray-500 hover:bg-gray-100 hover:text-gray-700'
        disabled={
          !currentPostSuggestion || currentPostSuggestion.AdType === 'text_only'
        }
        iconStart={<Icons.galleryEdit />}
      >
        {t('post_suggestion_preview.buttons.edit_image')}
      </Button>
    </div>
  );
}
