'use client';

// import { useSession } from 'next-auth/react';
import { useTranslations } from 'next-intl';
import { useEffect } from 'react';

import { useDirection } from '@/hooks/use-direction';

import { useUserByEmail } from '@/api/hooks/users/queries';
import { redirect } from '@/i18n/routing';
import { useCurrentUserStore } from '@/stores/current-user-store';

import { Skeleton } from '@/components/ui/skeleton';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';

export function OverviewHeader() {
  // const { data: session } = useSession();
  const t = useTranslations();
  const { locale } = useDirection();
  const { getUser } = useCurrentUserStore();
  const user = getUser();
  const { data, isPending, isError, error } = useUserByEmail(user?.email);

  useEffect(() => {
    if (!user) {
      redirect({ locale, href: '/login' });
    }
  }, [locale, user]);

  if (isError) {
    return <div>{error.message}</div>;
  }

  if (isPending) {
    return <Skeleton className='aspect-video h-12 w-full rounded-lg' />;
  }

  return (
    <div className='flex w-full items-center justify-between'>
      <h1 className='flex-1 text-xl font-semibold text-[#162F58]'>
        {t('welcome', { name: data?.UserName })}
      </h1>
      <ToggleGroup
        variant='default'
        className='inline-flex gap-0 rounded-xl bg-[#F5F7FA] p-0'
        type='single'
      >
        <ToggleGroupItem
          className='rounded-none rounded-s-xl px-2.5 py-6 text-xs font-normal'
          value='3_Months'
        >
          3 Months
        </ToggleGroupItem>
        <ToggleGroupItem
          className='rounded-none px-2.5 py-6 text-xs font-normal'
          value='Month'
        >
          Month
        </ToggleGroupItem>
        <ToggleGroupItem
          className='rounded-none px-2.5 py-6 text-xs font-normal'
          value='Week'
        >
          Week
        </ToggleGroupItem>
        <ToggleGroupItem
          className='rounded-none rounded-e-xl px-2.5 py-6 text-xs font-normal'
          value='Day'
        >
          Day
        </ToggleGroupItem>
      </ToggleGroup>
    </div>
  );
}
