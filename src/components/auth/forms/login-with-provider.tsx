'use client';

import { useTranslations } from 'next-intl';

import { Icons } from '@/components/shared/icons';
import { But<PERSON> } from '@/components/ui/button';

export function LoginWithProvider() {
  const t = useTranslations();

  return (
    <div className='grid grid-cols-3 gap-4'>
      <Button variant='outline' className='rounded-lg'>
        <Icons.google />
        <span className='text-xs font-light'>{t('google')}</span>
      </Button>
      <Button variant='outline' className='rounded-lg'>
        <Icons.microsoft />
        <span className='text-xs font-light'>{t('microsoft')}</span>
      </Button>

      <Button variant='outline' className='rounded-lg'>
        <Icons.linkedin />
        <span className='text-xs font-light'>{t('linkedin')}</span>
      </Button>
    </div>
  );
}
