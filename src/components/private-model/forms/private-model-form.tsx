import { useTranslations } from 'next-intl';
import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';

import { PrepareImageModelResponse } from '@/api/models/dtos/ai-model.dto';

import {
  useCreateLLMTextModel,
  useCreateImagePrivateModel,
  usePrepareImagePrivateModel,
} from '@/api/hooks/ai/mutations';
import { usePrepareTextPrivateModel } from '@/api/hooks/ai/queries';
import {
  prepareTextModelSchema,
  PrepareTextModelSchema,
  prepareImageModelSchema,
} from '@/api/models/schemas/private-model.schema';
import { useCurrentUserStore } from '@/stores/current-user-store';
import { usePrivateModelTextStore } from '@/stores/private-model-text-store';

import {
  privateModelTextFormOpts,
  createPrivateModelImageFormOpts,
  preparePrivateModelImageFormOpts,
} from '@/config/private-model/forms-config';

import { ImageModelForm } from '@/components/private-model/forms/image-model-form';
import { useAppForm } from '@/components/shared/form';
import { Button } from '@/components/ui/button';
import {
  DialogTitle,
  DialogClose,
  DialogFooter,
  DialogHeader,
  DialogContent,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Tabs, TabsList, TabsContent, TabsTrigger } from '@/components/ui/tabs';

import { PreviewTabContent } from '../preview-text-tab-content';
import ScrapedImageCard from '../scraped-image-card';
import { TextModelForm } from './text-model-form';

interface PrivateModelFormProps {
  tabName: 'preview' | 'info-general' | null;
  modelType: 'text' | 'image' | null;
  setTabName: (
    value: 'preview' | 'info-general' | null
  ) => Promise<URLSearchParams>;
  setModelType: (value: 'text' | 'image' | null) => Promise<URLSearchParams>;
}

export function PrivateModelForm({
  modelType,
  tabName,
  setTabName,
  setModelType,
}: PrivateModelFormProps) {
  const t = useTranslations();
  const { setTextFormValues, textFormValues } = usePrivateModelTextStore();
  const { getUser } = useCurrentUserStore();
  const user = getUser();
  const [scrapedData, setScrapedData] = useState<PrepareImageModelResponse>();

  const handleRemoveImage = (index: number) => {
    setScrapedData((prev) => {
      if (!prev?.ImageList) return prev;
      const newImageList = [...prev.ImageList];
      newImageList.splice(index, 1);

      return {
        ...prev,
        ImageList: newImageList,
      };
    });
  };
  const { data, isError } = usePrepareTextPrivateModel(
    textFormValues?.SocialMediaData[0].platform,
    textFormValues?.SocialMediaData[0].pageName
  );
  const { mutateAsync: createLLMTextModel } = useCreateLLMTextModel();

  const {
    mutateAsync: prepareImagePrivateModel,
    data: imageScrapedResults,
    isPending: isScrapingImages,
  } = usePrepareImagePrivateModel();

  useEffect(() => {
    if (imageScrapedResults) {
      setScrapedData(imageScrapedResults);
    }
  }, [imageScrapedResults]);

  const { mutateAsync: createImagePrivateModel } = useCreateImagePrivateModel();

  const textForm = useAppForm({
    defaultValues: {
      ...privateModelTextFormOpts.defaultValues,
      CompanyId: user?.companyId ?? '',
      UserEmail: user?.email ?? '',
      BrandId: user?.brandId ?? '',
    } as PrepareTextModelSchema,
    validators: {
      onChange: prepareTextModelSchema(t),
    },
    onSubmit: async ({ value }) => {
      if (isError || !data) {
        return;
      }

      await createLLMTextModel({
        ...value,
        SocialMediaData: data,
      });
    },
    onSubmitInvalid: () => {
      toast.error(t('form.invalid_form'));
    },
  });

  const prepareImageForm = useAppForm({
    defaultValues: {
      ...preparePrivateModelImageFormOpts.defaultValues,
      modelRequest: {
        ...preparePrivateModelImageFormOpts.defaultValues.modelRequest,
        CompanyId: user?.companyId ?? '',
        UserEmail: user?.email ?? '',
        BrandId: user?.brandId ?? '',
      },
    },
    validators: {
      onChange: prepareImageModelSchema(t),
    },
    onSubmit: async ({ value }) => {
      await prepareImagePrivateModel(value);
    },
    onSubmitInvalid: () => {
      toast.error(t('form.invalid_form'));
    },
  });

  const createImageForm = useAppForm({
    defaultValues: {
      ...createPrivateModelImageFormOpts.defaultValues,
    },
    onSubmit: async ({ value }) => {
      if (!scrapedData) return;
      await createImagePrivateModel({
        ...value,
        ...scrapedData,
        Tags: scrapedData?.Tags ?? [],
      });
    },
  });

  return (
    <DialogContent
      className='flex h-[90%] w-1/2 max-w-none flex-col justify-between gap-6 overflow-hidden px-0 py-4'
      onCloseAutoFocus={async () => {
        await Promise.all([setModelType(null), setTabName(null)]);
        textForm.reset();
        prepareImageForm.reset();
      }}
      onOpenAutoFocus={(e) => e.preventDefault()}
    >
      <DialogHeader>
        <DialogTitle className='px-6 text-base'>
          {t('private_model_form.new_model')}
        </DialogTitle>
      </DialogHeader>

      <div className='h-full space-y-4 overflow-y-auto px-6'>
        <Tabs
          value={tabName ?? 'info-general'}
          onValueChange={async (val) => {
            await setTabName(val as typeof tabName);
          }}
        >
          <TabsList className='flex h-auto rounded-none bg-transparent p-0'>
            <TabsTrigger
              value='info-general'
              className='relative flex-1 rounded-none py-2 after:absolute after:inset-x-0 after:bottom-0 after:h-0.5 data-[state=active]:bg-transparent data-[state=active]:text-primary data-[state=active]:shadow-none data-[state=active]:after:bg-primary'
            >
              {t('private_model_form.info_general')}
            </TabsTrigger>
            <TabsTrigger
              value='preview'
              className='relative flex-1 rounded-none py-2 after:absolute after:inset-x-0 after:bottom-0 after:h-0.5 data-[state=active]:bg-transparent data-[state=active]:text-primary data-[state=active]:shadow-none data-[state=active]:after:bg-primary'
            >
              {t('private_model_form.preview')}
            </TabsTrigger>
          </TabsList>

          <TabsContent value='info-general'>
            <RadioGroup
              className='grid grid-cols-2 gap-2'
              value={modelType ?? 'text'}
              onValueChange={async (value) => {
                await setModelType(value as typeof modelType);
              }}
            >
              <div className='flex items-center justify-start gap-2 rounded-lg border border-primary px-2 py-4 rtl:flex-row-reverse'>
                <RadioGroupItem
                  value='text'
                  id='text'
                  className='size-4'
                  indicatorIconClassName='size-2'
                />
                <Label htmlFor='text' className='text-xs text-foreground'>
                  {t('private_model_form.text_model')}
                </Label>
              </div>

              <div className='flex items-center justify-start gap-2 rounded-lg border px-1 py-1 rtl:flex-row-reverse'>
                <RadioGroupItem
                  value='image'
                  id='image'
                  className='size-4'
                  indicatorIconClassName='size-2'
                />
                <div className='flex flex-1 flex-col items-start rtl:items-end'>
                  <Label htmlFor='image' className='text-xs text-foreground'>
                    {t('private_model_form.image_model')}
                  </Label>
                </div>
              </div>
            </RadioGroup>
            {modelType === 'text' && <TextModelForm form={textForm} />}
            {modelType === 'image' && (
              <ImageModelForm form={prepareImageForm} />
            )}
          </TabsContent>

          <TabsContent value='preview'>
            {modelType === 'text' && <PreviewTabContent />}
            {modelType === 'image' && (
              <ScrapedImageCard
                imageScrapedResults={scrapedData}
                isLoading={isScrapingImages}
                onRemoveImage={handleRemoveImage}
              />
            )}
          </TabsContent>
        </Tabs>
      </div>
      <DialogFooter className='px-6'>
        <DialogClose asChild>
          <Button
            type='button'
            variant='outline'
            onClick={async () => {
              await Promise.all([setModelType(null), setTabName(null)]);
              textForm.reset();
              prepareImageForm.reset();
            }}
          >
            {t('private_model_form.cancel')}
          </Button>
        </DialogClose>
        {tabName === 'preview' ? (
          modelType === 'text' ? (
            <textForm.AppForm>
              <DialogClose asChild>
                <textForm.SubmitButton
                  onClick={async () => {
                    await textForm.handleSubmit();
                  }}
                >
                  {t('private_model_form.save')}
                </textForm.SubmitButton>
              </DialogClose>
            </textForm.AppForm>
          ) : (
            <prepareImageForm.AppForm>
              <DialogClose asChild>
                <prepareImageForm.SubmitButton
                  onClick={async () => {
                    if (!scrapedData) return;
                    await createImageForm.handleSubmit();
                  }}
                >
                  {t('private_model_form.save')}
                </prepareImageForm.SubmitButton>
              </DialogClose>
            </prepareImageForm.AppForm>
          )
        ) : (
          <>
            <Button
              type='button'
              onClick={async () => {
                if (modelType === 'text') {
                  setTextFormValues(textForm.state.values);
                  await textForm.handleSubmit();
                  if (textForm.state.canSubmit) {
                    await setTabName('preview');
                  }
                  return;
                } else if (modelType === 'image') {
                  await setTabName('preview');
                  await prepareImageForm.handleSubmit();
                  return;
                }
              }}
            >
              {t('private_model_form.preview')}
            </Button>
          </>
        )}
      </DialogFooter>
    </DialogContent>
  );
}
