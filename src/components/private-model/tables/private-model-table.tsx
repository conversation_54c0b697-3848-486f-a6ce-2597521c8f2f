'use client';

import { format } from 'date-fns';
import { useTranslations } from 'next-intl';
import { useRef, useState } from 'react';
import { toast } from 'sonner';

import {
  Search,
  ChevronUp,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';

import { cn } from '@/lib/utils';

import {
  useDeletePrivateModel,
  useMarkPrivateModelAsFavorite,
} from '@/api/hooks/ai/mutations';
import { useAllByCompanyBrandId } from '@/api/hooks/ai/queries';
import {
  flexRender,
  SortingState,
  useReactTable,
  PaginationState,
  VisibilityState,
  getCoreRowModel,
  getSortedRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
  getPaginationRowModel,
  getFacetedUniqueValues,
} from '@tanstack/react-table';

import { Icons } from '@/components/shared/icons';
import TableSkeleton from '@/components/shared/table-skeleton';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardHeader, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuRadioItem,
  DropdownMenuRadioGroup,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectItem,
  SelectValue,
  SelectContent,
  SelectTrigger,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableRow,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
} from '@/components/ui/table';

import { usePrivateModelColumns } from './columns';

export function PrivateModelTable() {
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('grid');
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [selectedDate, setSelectedDate] = useState<Date | undefined>();
  const [deletingId, setDeletingId] = useState<string | undefined>();
  const [startingId, setStartingId] = useState<string | undefined>();

  const inputRef = useRef<HTMLInputElement>(null);

  const t = useTranslations();

  const {
    data: allPrivateModels,
    refetch,
    isPending,
    isError,
    error,
    isRefetching,
  } = useAllByCompanyBrandId();

  const { mutateAsync: handleDeleteClick } = useDeletePrivateModel();
  const { mutateAsync: handleStarClick } = useMarkPrivateModelAsFavorite();
  const columns = usePrivateModelColumns({
    t,
    onDelete: async (id: string) => {
      setDeletingId(id);
      const toastId = toast.loading(t('toast.deleting_model'));

      try {
        await handleDeleteClick(id);

        toast.success(t('toast.model_deleted'), { id: toastId });
      } catch (error: unknown) {
        if (error instanceof Error) {
          toast.error(t('toast.deletion_failed'), { id: toastId });
        }
      } finally {
        await refetch();
        setDeletingId(undefined);
      }
    },
    onStar: async (id: string) => {
      setStartingId(id);
      try {
        await handleStarClick(id);
      } catch (error) {
        console.log(error);
      } finally {
        await refetch();
        setStartingId(undefined);
      }
    },
  });

  const table = useReactTable({
    data: allPrivateModels ?? [],
    columns,
    state: {
      sorting,
      pagination,
      columnFilters,
      columnVisibility,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  });

  if (isError) {
    return <div>{error.message}</div>;
  }

  if (isPending) {
    return <TableSkeleton />;
  }

  return (
    <Card>
      <CardHeader className='px-6 py-4'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <div className='relative w-[300px]'>
              <Input
                placeholder={t('search')}
                className='border-none bg-[#F4F4F5] focus-visible:ring-0 focus-visible:ring-offset-0'
                ref={inputRef}
                value={
                  (table.getColumn('ModelName')?.getFilterValue() as string) ??
                  ''
                }
                onChange={(e) =>
                  table.getColumn('ModelName')?.setFilterValue(e.target.value)
                }
              />
              <Search
                className='absolute end-2 top-1/2 -translate-y-1/2 transform text-gray-400'
                size={18}
              />
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant='ghost' className='bg-[#F4F4F5]'>
                  <Icons.filter className='mr-1 text-xs' />
                  {t('filter')}
                  {columnFilters.length > 0 && (
                    <span className='ml-2 rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800'>
                      {columnFilters.length}
                    </span>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className='w-64 p-4'>
                <div className='space-y-4'>
                  <div>
                    <label className='mb-2 block text-sm font-medium'>
                      {t('model_type')}
                    </label>
                    <Select
                      value={
                        (table
                          .getColumn('ModelType')
                          ?.getFilterValue() as string) ?? ''
                      }
                      onValueChange={(value) =>
                        table.getColumn('ModelType')?.setFilterValue(value)
                      }
                    >
                      <SelectTrigger className='w-full'>
                        <SelectValue placeholder='Select type' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='LLM'>{t('text')}</SelectItem>
                        <SelectItem value='IMAGE'>{t('image')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className='flex justify-between gap-2'>
                    <Button
                      variant='outline'
                      size='sm'
                      onClick={() => {
                        table.resetColumnFilters();
                        table.getColumn('ModelName')?.setFilterValue('');
                      }}
                    >
                      {t('clear_all')}
                    </Button>
                    <Button
                      variant='default'
                      size='sm'
                      onClick={() =>
                        document.dispatchEvent(
                          new KeyboardEvent('keydown', { key: 'Escape' })
                        )
                      }
                    >
                      {t('apply')}
                    </Button>
                  </div>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant='ghost'
                  className='border-gray-200 bg-[#F4F4F5]'
                >
                  <Icons.sort className='mr-1 text-xs' />
                  {t('sort')}
                  {sorting.length > 0 && (
                    <span className='ml-2 rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800'>
                      {sorting[0].id} ({sorting[0].desc ? 'Desc' : 'Asc'})
                    </span>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className='w-48'>
                <DropdownMenuRadioGroup
                  value={
                    sorting[0]?.id + '-' + (sorting[0]?.desc ? 'desc' : 'asc')
                  }
                  onValueChange={(value) => {
                    const [id, direction] = value.split('-');
                    setSorting([{ id, desc: direction === 'desc' }]);
                  }}
                >
                  {table
                    .getAllColumns()
                    .filter((column) => column.getCanSort())
                    .map((column) => (
                      <div key={column.id}>
                        <DropdownMenuRadioItem
                          value={`${column.id}-asc`}
                          className='cursor-pointer'
                        >
                          {column.columnDef.header as string} ↑{' '}
                        </DropdownMenuRadioItem>
                        <DropdownMenuRadioItem
                          value={`${column.id}-desc`}
                          className='cursor-pointer'
                        >
                          {column.columnDef.header as string} ↓
                        </DropdownMenuRadioItem>
                      </div>
                    ))}
                </DropdownMenuRadioGroup>

                {sorting.length > 0 && (
                  <div className='border-t p-2'>
                    <Button
                      variant='ghost'
                      size='sm'
                      className='w-full'
                      onClick={() => setSorting([])}
                    >
                      {t('clear_sorting')}
                    </Button>
                  </div>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className='flex items-center gap-2'>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant='ghost'
                  className={cn(
                    'w-[240px] justify-between bg-[#F4F4F5] text-[#71717A]',
                    selectedDate && 'border-blue-200 bg-blue-50'
                  )}
                >
                  <span className='text-xs text-gray-500'>
                    {selectedDate
                      ? format(selectedDate, 'yyyy-MM-dd')
                      : t('select_a_date')}
                  </span>
                  <Icons.calendarInput className='h-4 w-4' />
                </Button>
              </PopoverTrigger>
              <PopoverContent className='w-auto p-0' align='end'>
                <Calendar
                  mode='single'
                  selected={selectedDate}
                  onSelect={(date) => {
                    setSelectedDate(date);
                    table.getColumn('CreationTimestamp')?.setFilterValue(date);
                  }}
                  initialFocus
                />
                {selectedDate && (
                  <div className='border-t p-2'>
                    <Button
                      variant='ghost'
                      size='sm'
                      className='w-full'
                      onClick={() => {
                        setSelectedDate(undefined);
                        table
                          .getColumn('CreationTimestamp')
                          ?.setFilterValue(undefined);
                      }}
                    >
                      {t('clear')}
                    </Button>
                  </div>
                )}
              </PopoverContent>
            </Popover>
            <div className='flex items-center gap-2'>
              <button
                className={cn(
                  'rounded-md border-none px-1.5 py-1',
                  viewMode === 'list'
                    ? 'bg-[#D7E4F8] text-primary'
                    : 'bg-[#F4F4F5] text-[#71717A] hover:bg-[#D7E4F8] hover:text-primary'
                )}
                onClick={() => setViewMode('list')}
              >
                <Icons.list className='h-7 w-6' />
              </button>
              <button
                className={cn(
                  'rounded-md border-none px-2.5 py-2',
                  viewMode === 'grid'
                    ? 'bg-[#D7E4F8] text-primary'
                    : 'bg-[#F4F4F5] text-[#71717A] hover:bg-[#D7E4F8] hover:text-primary'
                )}
                onClick={() => setViewMode('grid')}
              >
                <Icons.listGrid className='h-5 w-4' />
              </button>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <div className='flex flex-1 flex-col'>
          <div
            className={cn('flex-1 overflow-hidden rounded-lg bg-background')}
          >
            {viewMode === 'list' ? (
              <ScrollArea type='always' className='h-full'>
                <Table>
                  <TableHeader>
                    {table.getHeaderGroups().map((headerGroup) => (
                      <TableRow key={headerGroup.id}>
                        {headerGroup.headers.map((header) => (
                          <TableHead
                            key={header.id}
                            style={{ width: header.getSize() }}
                          >
                            {header.isPlaceholder ? null : (
                              <div
                                className={cn(
                                  'flex items-center gap-2',
                                  header.column.getCanSort() && 'cursor-pointer'
                                )}
                                onClick={header.column.getToggleSortingHandler()}
                              >
                                {flexRender(
                                  header.column.columnDef.header,
                                  header.getContext()
                                )}
                                {{
                                  asc: <ChevronUp size={16} />,
                                  desc: <ChevronDown size={16} />,
                                }[header.column.getIsSorted() as string] ??
                                  null}
                              </div>
                            )}
                          </TableHead>
                        ))}
                      </TableRow>
                    ))}
                  </TableHeader>
                  <TableBody>
                    {table.getRowModel().rows.length === 0 ? (
                      <TableRow>
                        <TableCell
                          colSpan={columns.length}
                          className='h-24 text-center text-muted-foreground'
                        >
                          No data available
                        </TableCell>
                      </TableRow>
                    ) : (
                      table.getRowModel().rows.map((row) => (
                        <TableRow key={row.id}>
                          {row.getVisibleCells().map((cell) => (
                            <TableCell key={cell.id}>
                              {row.original.Id === deletingId ||
                              isRefetching ||
                              startingId ? (
                                <Skeleton
                                  style={{
                                    width: cell.column.getSize(),
                                  }}
                                  className='h-7'
                                />
                              ) : (
                                flexRender(
                                  cell.column.columnDef.cell,
                                  cell.getContext()
                                )
                              )}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </ScrollArea>
            ) : (
              <div className='grid grid-cols-1 gap-4 p-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'>
                {table.getRowModel().rows.length === 0 ? (
                  <div className='col-span-full flex h-24 items-center justify-center text-center text-muted-foreground'>
                    No data available
                  </div>
                ) : (
                  table.getRowModel().rows.map((row) => (
                    <div
                      key={row.id}
                      className='rounded-lg border bg-white p-4 transition-shadow hover:shadow-md'
                    >
                      <div className='flex h-full flex-col justify-between'>
                        <div className='mb-2 flex flex-col gap-1'>
                          {row
                            .getVisibleCells()
                            .filter(
                              (cell) =>
                                !['ModelType', 'actions'].includes(
                                  cell.column.id
                                )
                            )
                            .map((cell) => (
                              <div
                                key={cell.id}
                                className='text-sm text-gray-900'
                              >
                                {row.original.Id === deletingId ||
                                isRefetching ||
                                startingId ? (
                                  <Skeleton className='h-4 w-[100px]' />
                                ) : (
                                  flexRender(
                                    cell.column.columnDef.cell,
                                    cell.getContext()
                                  )
                                )}
                              </div>
                            ))}
                        </div>

                        <div className='mt-2 flex items-center justify-between'>
                          <div>
                            {row.original.Id === deletingId ||
                            isRefetching ||
                            startingId ? (
                              <Skeleton className='h-6 w-[80px] rounded-xl' />
                            ) : (
                              row
                                .getVisibleCells()
                                .filter(
                                  (cell) => cell.column.id === 'ModelType'
                                )
                                .map((cell) => (
                                  <div key={cell.id}>
                                    {flexRender(
                                      cell.column.columnDef.cell,
                                      cell.getContext()
                                    )}
                                  </div>
                                ))
                            )}
                          </div>

                          <div>
                            {row.original.Id === deletingId ||
                            isRefetching ||
                            startingId ? (
                              <div className='flex gap-2'>
                                <Skeleton className='h-8 w-8 rounded-full' />
                                <Skeleton className='h-8 w-8 rounded-full' />
                                <Skeleton className='h-8 w-8 rounded-full' />
                              </div>
                            ) : (
                              row
                                .getVisibleCells()
                                .filter((cell) => cell.column.id === 'actions')
                                .map((cell) => (
                                  <div key={cell.id} className='flex gap-2'>
                                    {flexRender(
                                      cell.column.columnDef.cell,
                                      cell.getContext()
                                    )}
                                  </div>
                                ))
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            )}
          </div>
          <div className='mt-4 flex items-center justify-between'>
            <div className='flex items-center gap-3'>
              <Select
                value={table.getState().pagination.pageSize.toString()}
                onValueChange={(v) => table.setPageSize(Number(v))}
              >
                <SelectTrigger className='w-[120px]'>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {[5, 10, 20].map((size) => (
                    <SelectItem key={size} value={size.toString()}>
                      {t('show')} {size}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className='flex items-center gap-2'>
              <Button
                variant='outline'
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                <ChevronLeft className='mr-2' size={16} />
                {t('previous')}
              </Button>
              <Button
                variant='outline'
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
              >
                {t('next')}
                <ChevronRight className='ml-2' size={16} />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
