import { format } from 'date-fns';
import { useState } from 'react';

import { Search, ChevronLeft, ChevronRight } from 'lucide-react';

import { cn } from '@/lib/utils';

import { usePagination } from '@/hooks/use-pagination';

import {
  ColumnDef,
  flexRender,
  SortingState,
  useReactTable,
  getCoreRowModel,
  VisibilityState,
  PaginationState,
  getSortedRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
  getPaginationRowModel,
} from '@tanstack/react-table';

import { Icons } from '@/components/shared/icons';
import TableSkeleton from '@/components/shared/table-skeleton';
import { Button, buttonVariants } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardHeader, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Pagination,
  PaginationItem,
  PaginationLink,
  PaginationContent,
  PaginationEllipsis,
} from '@/components/ui/pagination';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Table,
  TableRow,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
} from '@/components/ui/table';

export interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  isLoading?: boolean;
  isError?: boolean;
  error?: Error | null;
}

export function DataTable<TData, TValue>({
  columns,
  data,
  isLoading,
  isError,
  error,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const [{ pageIndex, pageSize }, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [selectedDate, setSelectedDate] = useState<Date | undefined>();

  const pagination = {
    pageIndex,
    pageSize,
  };

  const { pages, showLeftEllipsis, showRightEllipsis } = usePagination({
    currentPage: pageIndex + 1,
    totalPages: Math.ceil(data.length / pageSize),
    paginationItemsToDisplay: 5,
  });

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onPaginationChange: setPagination,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination,
    },
  });

  if (isLoading) {
    return <TableSkeleton />;
  }

  if (isError && error) {
    return <div>{error.message}</div>;
  }

  return (
    <Card>
      <CardHeader className='px-6 py-4'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <div className='relative w-[300px]'>
              <Input
                placeholder='Search'
                className='border-none bg-[#F4F4F5] focus-visible:ring-0 focus-visible:ring-offset-0'
                value={
                  (table.getColumn('Ad.title')?.getFilterValue() as string) ??
                  ''
                }
                onChange={(e) =>
                  table.getColumn('Ad.title')?.setFilterValue(e.target.value)
                }
              />
              <Search
                className='absolute right-3 top-1/2 -translate-y-1/2 text-gray-400'
                size={18}
              />
            </div>
          </div>
          <div className='flex items-center gap-2'>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant='ghost'
                  className={cn(
                    'w-[240px] justify-between bg-[#F4F4F5] text-[#71717A]',
                    selectedDate && 'border-blue-200 bg-blue-50'
                  )}
                >
                  <span className='text-xs text-gray-500'>
                    {selectedDate
                      ? format(selectedDate, 'yyyy-MM-dd')
                      : 'Select a date'}
                  </span>
                  <Icons.calendarInput className='h-4 w-4' />
                </Button>
              </PopoverTrigger>
              <PopoverContent className='w-auto p-0' align='end'>
                <Calendar
                  mode='single'
                  selected={selectedDate}
                  onSelect={(date) => {
                    setSelectedDate(date);
                    table.getColumn('CreationTimestamp')?.setFilterValue(date);
                  }}
                  initialFocus
                />
                {selectedDate && (
                  <div className='border-t p-2'>
                    <Button
                      variant='ghost'
                      size='sm'
                      className='w-full'
                      onClick={() => {
                        setSelectedDate(undefined);
                        table
                          .getColumn('CreationTimestamp')
                          ?.setFilterValue(undefined);
                      }}
                    >
                      Clear
                    </Button>
                  </div>
                )}
              </PopoverContent>
            </Popover>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <div className='flex flex-1 flex-col'>
          <div
            className={cn('flex-1 overflow-hidden rounded-lg bg-background')}
          >
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => {
                      return (
                        <TableHead
                          className='h-11 bg-[#F4F4F5] font-medium text-muted-foreground'
                          key={header.id}
                        >
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                        </TableHead>
                      );
                    })}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && 'selected'}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className='h-24 text-center'
                    >
                      No results.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      </CardContent>

      <div className='flex justify-start space-x-2 py-4'>
        <Pagination>
          <PaginationContent className='shadow-xs inline-flex gap-0 -space-x-px rounded-md rtl:space-x-reverse'>
            <PaginationItem className='[&:first-child>a]:rounded-s-md [&:last-child>a]:rounded-e-md'>
              <PaginationLink
                className={cn(
                  buttonVariants({
                    variant: 'outline',
                  }),
                  'rounded-none shadow-none focus-visible:z-10 aria-disabled:pointer-events-none [&[aria-disabled]>svg]:opacity-50'
                )}
                href='#'
                onClick={(e) => {
                  e.preventDefault();
                  table.previousPage();
                }}
                aria-disabled={!table.getCanPreviousPage()}
                aria-label='Go to previous page'
              >
                <ChevronLeft className='h-4 w-4' aria-hidden='true' />
              </PaginationLink>
            </PaginationItem>

            {showLeftEllipsis && (
              <PaginationItem className='[&:first-child>a]:rounded-s-md [&:last-child>a]:rounded-e-md'>
                <PaginationEllipsis />
              </PaginationItem>
            )}

            {pages.map((page) => (
              <PaginationItem key={page}>
                <PaginationLink
                  className={cn(
                    buttonVariants({
                      variant: 'outline',
                    }),
                    'rounded-none shadow-none focus-visible:z-10',
                    page === pageIndex + 1 && 'bg-accent'
                  )}
                  href='#'
                  onClick={(e) => {
                    e.preventDefault();
                    table.setPageIndex(page - 1);
                  }}
                  isActive={page === pageIndex + 1}
                >
                  {page}
                </PaginationLink>
              </PaginationItem>
            ))}

            {showRightEllipsis && (
              <PaginationItem className='[&:first-child>a]:rounded-s-md [&:last-child>a]:rounded-e-md'>
                <PaginationEllipsis
                  className={cn(
                    buttonVariants({
                      variant: 'outline',
                    }),
                    'pointer-events-none rounded-none shadow-none'
                  )}
                />
              </PaginationItem>
            )}

            <PaginationItem className='[&:first-child>a]:rounded-s-md [&:last-child>a]:rounded-e-md'>
              <PaginationLink
                className={cn(
                  buttonVariants({
                    variant: 'outline',
                  }),
                  'rounded-none shadow-none focus-visible:z-10 aria-disabled:pointer-events-none [&[aria-disabled]>svg]:opacity-50'
                )}
                href='#'
                onClick={(e) => {
                  e.preventDefault();
                  table.nextPage();
                }}
                aria-disabled={!table.getCanNextPage()}
                aria-label='Go to next page'
              >
                <ChevronRight className='h-4 w-4' aria-hidden='true' />
              </PaginationLink>
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </Card>
  );
}
