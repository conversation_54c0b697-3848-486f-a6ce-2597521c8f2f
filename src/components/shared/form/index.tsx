'use client';

import { createF<PERSON><PERSON><PERSON>, createFormHookContexts } from '@tanstack/react-form';

import { FileUploadField } from '@/components/shared/form/file-upload-field';
import { RadioGroupField } from '@/components/shared/form/radio-group-field';
import { TabsField } from '@/components/shared/form/tabs-field';

import { CheckboxField } from './checkbox-field';
import { SelectField } from './select-field';
import { SliderWithInputField } from './slider-with-input-field';
import { SubmitButton } from './submit-button';
import { TextField } from './text-field';
import { TextFieldWithInnerTags } from './text-field-with-inner-tags';
import { TextareaField } from './textarea-field';

export const { fieldContext, useFieldContext, formContext, useFormContext } =
  createFormHookContexts();

export const { useAppForm, withForm } = createFormHook({
  fieldComponents: {
    TextField,
    TabsField,
    RadioGroupField,
    TextFieldWithInnerTags,
    TextareaField,
    SliderWithInputField,
    CheckboxField,
    FileUploadField,
    SelectField,
  },
  formComponents: {
    SubmitButton,
  },
  fieldContext,
  formContext,
});
