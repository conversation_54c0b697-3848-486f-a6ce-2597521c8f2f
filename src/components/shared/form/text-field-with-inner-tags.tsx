'use client';

import { Tag, TagInput, TagInputProps } from 'emblor';
import React, { useState, useEffect } from 'react';

import { PlusIcon } from 'lucide-react';

import { cn } from '@/lib/utils';

import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';

import { useFieldContext } from '.';
import { FieldErrors } from './field-errors';

interface TextFieldWithInnerTagsProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  labelClassName?: string;
  required?: boolean;
  showSeparateAddButton?: boolean;
  separateAddButtonText?: string;
  inlineTags?: boolean;
  tagInputProps?: TagInputProps;
}

export const TextFieldWithInnerTags = ({
  label,
  labelClassName,
  required = true,
  showSeparateAddButton = false,
  separateAddButtonText = 'Add',
  inlineTags = false,
  tagInputProps,
}: TextFieldWithInnerTagsProps) => {
  const field = useFieldContext<string[]>();

  const [tagsData, setTagsData] = useState<Tag[]>([]);
  const [activeTagIndex, setActiveTagIndex] = useState<number | null>(null);
  const [tagInputValue, setTagInputValue] = useState<string>('');

  useEffect(() => {
    if (tagsData.length > 0) {
      field.setValue(tagsData.map((tag) => tag.text));
    }
  }, [field, tagsData]);

  const addTag = () => {
    if (!isTagValid(tagInputValue, tagsData)) {
      setTagInputValue('');
      return;
    }

    const newTag: Tag = {
      id: crypto.randomUUID(),
      text: tagInputValue,
    };

    setTagsData((prev) => [...prev, newTag]);
    setTagInputValue('');
  };

  return (
    <div className='space-y-2'>
      <div className='grid gap-2'>
        <Label htmlFor={field.name} className={cn(labelClassName)}>
          {label}
          {required && <span className='ms-0.5 text-destructive'>*</span>}
        </Label>

        <div className='flex gap-2'>
          <TagInput
            id={field.name}
            tags={tagsData}
            setTags={(newTags) => {
              setTagsData(newTags);
              setTagInputValue('');
            }}
            onTagRemove={(tag) => {
              const tagIndex = tagsData.findIndex((t) => t.text === tag);
              field.setValue((tagsData) =>
                tagsData.filter((_, index) => index !== tagIndex)
              );
            }}
            onInputChange={(value) => {
              setTagInputValue(value);
            }}
            inputProps={{
              value: tagInputValue,
            }}
            activeTagIndex={activeTagIndex}
            setActiveTagIndex={setActiveTagIndex}
            onBlur={field.handleBlur}
            inputFieldPosition='top'
            inlineTags={inlineTags}
            styleClasses={{
              tagList: {
                container: 'gap-1',
              },
              input:
                'rounded-md transition-[color,box-shadow] placeholder:text-muted-foreground/70 focus-visible:border-ring outline-none focus-visible:ring-[3px] focus-visible:ring-ring/50',
              tag: {
                body: 'relative h-7 bg-background border border-input hover:bg-background rounded-md font-medium text-xs ps-2 !pe-7',
                closeButton:
                  'absolute -inset-y-px -end-px p-0 rounded-s-none rounded-e-md flex size-7 transition-[color,box-shadow] outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] text-muted-foreground/80 hover:text-foreground',
              },
            }}
            {...tagInputProps}
          />
          <Button
            type='button'
            variant='outline'
            className={cn(
              'rounded-xl bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-700',
              {
                hidden: !showSeparateAddButton,
              }
            )}
            iconStart={<PlusIcon className='size-4' />}
            onClick={addTag}
          >
            {separateAddButtonText}
          </Button>
        </div>
      </div>
      <FieldErrors meta={field.state.meta} />
    </div>
  );
};

/**
 * Validates a tag value against existing tags to ensure it's valid for addition.
 *
 * @param tagText - The text value of the tag to validate
 * @param existingTags - Array of current Tag objects to check against
 * @returns True if the tag is valid (non-empty and not a duplicate), false otherwise
 *
 * @example
 * // Check if a tag is valid
 * const isValid = isTagValid("new tag", currentTags);
 * if (isValid) {
 *   // Add the tag
 * }
 */
const isTagValid = (tagText: string, existingTags: Tag[]): boolean => {
  const trimmedTag = tagText.trim();
  const tagExists = existingTags.some((tag) => tag.text === trimmedTag);

  return trimmedTag !== '' && !tagExists;
};
