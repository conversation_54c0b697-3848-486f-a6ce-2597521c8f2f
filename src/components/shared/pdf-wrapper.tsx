import 'react-pdf/dist/Page/TextLayer.css';
import 'react-pdf/dist/Page/AnnotationLayer.css';
import { useRef, useState, useEffect } from 'react';
import { Page, pdfjs, Document } from 'react-pdf';

import { ChevronLeft, ChevronRight } from 'lucide-react';

pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`;

interface PdfWrapperProps {
  src: string;
  fileName?: string;
  height?: string;
}

export default function PdfWrapper({ src, fileName, height }: PdfWrapperProps) {
  const [numPages, setNumPages] = useState<number>(0);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [pageSize, setPageSize] = useState<{ width: number; height: number }>({
    width: 0,
    height: 0,
  });

  const containerRef = useRef<HTMLDivElement>(null);

  function onDocumentLoadSuccess({ numPages }: { numPages: number }): void {
    setNumPages(numPages);
    setPageNumber(1);
  }

  function nextPage() {
    setPageNumber((prev) => Math.min(numPages, prev + 1));
  }

  function prevPage() {
    setPageNumber((prev) => Math.max(1, prev - 1));
  }

  useEffect(() => {
    function updatePageSize() {
      if (containerRef.current) {
        const containerWidth = containerRef.current.clientWidth;
        const containerHeight = containerRef.current.clientHeight;

        // Aspect ratio for standard PDFs (8.5 x 11 inches)
        const aspectRatio = 8.5 / 11;

        let width = containerWidth;
        let heightCalc = width / aspectRatio;

        if (heightCalc > containerHeight) {
          heightCalc = containerHeight;
          width = heightCalc * aspectRatio;
        }

        setPageSize({ width, height: heightCalc });
      }
    }

    updatePageSize(); // Run on mount
    const resizeHandler = () => {
      updatePageSize();
    };

    window.addEventListener('resize', resizeHandler);
    return () => window.removeEventListener('resize', resizeHandler);
  }, []);

  return (
    <div
      ref={containerRef}
      className='group relative aspect-[8.5/11] w-full'
      style={{
        height: height || '100vh',
      }}
    >
      {/* === Top bar (title + total pages) === */}
      <div className='pointer-events-none absolute left-0 right-0 top-0 z-10 flex h-6 items-center justify-between bg-gray-800 px-4 py-2 text-white opacity-0 transition-opacity duration-300 group-hover:opacity-90'>
        <span className='font-semibold'>{fileName ?? 'No Title'}</span>
        <span className='text-sm'>
          {numPages > 0 ? `${numPages} page${numPages > 1 ? 's' : ''}` : ''}
        </span>
      </div>

      {numPages > 1 && (
        <>
          {/* === Left (Previous page) button === */}
          <button
            onClick={prevPage}
            disabled={pageNumber <= 1}
            className='absolute left-2 top-1/2 z-10 flex h-6 w-4 -translate-y-1/2 items-center justify-center rounded-full bg-gray-800 px-3 py-2 text-white opacity-0 transition-opacity duration-300 hover:bg-gray-700 disabled:cursor-not-allowed disabled:opacity-0 group-hover:opacity-90'
          >
            <ChevronRight className='text-slate-400' size={15} />
          </button>

          {/* === Right (Next page) button === */}
          <button
            onClick={nextPage}
            disabled={pageNumber >= numPages}
            className='absolute right-2 top-1/2 z-10 flex h-6 w-4 -translate-y-1/2 items-center justify-center rounded-full bg-gray-800 px-3 py-2 text-white opacity-0 transition-opacity duration-300 hover:bg-gray-700 disabled:cursor-not-allowed disabled:opacity-0 group-hover:opacity-90'
          >
            <ChevronLeft className='text-slate-400' size={15} />
          </button>
        </>
      )}

      {/* === PDF Document itself === */}
      <div className='flex h-full items-center justify-center'>
        <Document
          file={src}
          onLoadSuccess={onDocumentLoadSuccess}
          className='my-react-pdf'
        >
          <Page pageNumber={pageNumber} width={pageSize.width} />
        </Document>
      </div>

      {/* === Bottom bar (slider) === */}
      <div className='absolute bottom-0 left-0 right-0 z-10 flex h-6 items-center gap-4 bg-gray-800 px-4 py-2 text-white opacity-0 transition-opacity duration-300 group-hover:opacity-90'>
        <input
          type='range'
          className='flex-1'
          min={0}
          max={numPages}
          disabled={numPages === 0}
          value={pageNumber}
          onChange={(e) => setPageNumber(Number(e.target.value))}
        />
        <p className='whitespace-nowrap'>
          Page {pageNumber} / {numPages || '—'}
        </p>
      </div>
    </div>
  );
}
