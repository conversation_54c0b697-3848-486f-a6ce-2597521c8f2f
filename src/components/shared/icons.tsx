import React from 'react';

import Bookmark from '@/assets/icons/bookmark.svg';
import X from '@/assets/icons/brand-x.svg';
import CalendarAdd from '@/assets/icons/calendar-add.svg';
import CalendarInput from '@/assets/icons/calendar-input.svg';
import Calendar from '@/assets/icons/calendar.svg';
import CirclePlay from '@/assets/icons/circle-play.svg';
import CloudUpload from '@/assets/icons/cloud-upload.svg';
import Crown from '@/assets/icons/crown.svg';
import EmojiIcon from '@/assets/icons/emoji-icon.svg';
import Eye from '@/assets/icons/eye.svg';
import FacebookChat from '@/assets/icons/facebook-chat.svg';
import FacebookLike from '@/assets/icons/facebook-like.svg';
import FacebookShare from '@/assets/icons/facebook-share.svg';
import Facebook from '@/assets/icons/facebook.svg';
import Filter from '@/assets/icons/filter.svg';
import FolderOpen from '@/assets/icons/folder-open.svg';
import GalleryEdit from '@/assets/icons/gallery-edit.svg';
import GalleryHorizontal from '@/assets/icons/gallery-horizontal.svg';
import Hashtag from '@/assets/icons/hastag.svg';
import Headset from '@/assets/icons/headset.svg';
import Instagram from '@/assets/icons/instagram.svg';
import LayoutDashboard from '@/assets/icons/layout-dashboard.svg';
import LinkedinComment from '@/assets/icons/linkedin-comment.svg';
import LinkedinRepost from '@/assets/icons/linkedin-repost.svg';
import LinkedinSend from '@/assets/icons/linkedin-send.svg';
import LinkedinThumbsUp from '@/assets/icons/linkedin-thumbs-up.svg';
import Linkedin from '@/assets/icons/linkedin.svg';
import ListGrid from '@/assets/icons/list-grid.svg';
import List from '@/assets/icons/list.svg';
import LoginIcon from '@/assets/icons/login.svg';
import MagicStick from '@/assets/icons/magic-stick.svg';
import Megaphone from '@/assets/icons/megaphone.svg';
import PenNew from '@/assets/icons/pen-new.svg';
import RefreshSquare from '@/assets/icons/refresh-square.svg';
import Sort from '@/assets/icons/sort.svg';
import Sparkles from '@/assets/icons/sparkles.svg';
import Star from '@/assets/icons/star.svg';
import Swap from '@/assets/icons/swap.svg';
import Trash from '@/assets/icons/trash.svg';
import XBookmark from '@/assets/icons/x-bookmark.svg';
import XLike from '@/assets/icons/x-like.svg';
import XReply from '@/assets/icons/x-reply.svg';
import XRepost from '@/assets/icons/x-repost.svg';
import XShare from '@/assets/icons/x-share.svg';
import XView from '@/assets/icons/x-view.svg';

type IconProps = React.SVGAttributes<SVGElement>;

export const Icons = {
  logoDark: (props: IconProps) => (
    <svg
      width='55'
      height='62'
      viewBox='0 0 55 62'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <g clipPath='url(#clip0_514_29887)'>
        <path
          d='M52.6184 0C53.8754 0.492302 54.2696 1.35634 54.2494 2.56198C54.1045 11.2828 50.6067 18.7845 44.1266 25.2212C43.2539 26.0886 42.3171 26.9058 41.4274 27.7631C40.986 28.1885 40.5917 28.1952 40.0896 27.8301C37.222 25.747 34.3408 23.6807 31.4664 21.6077C30.7925 21.1221 30.7722 20.824 31.4058 20.2949C33.6635 18.4027 35.7595 16.3833 37.4511 14.049C39.9987 10.5326 41.2084 6.66114 41.2421 2.495C41.2522 1.30946 41.66 0.468859 42.8933 0C46.135 0 49.3801 0 52.6218 0L52.6184 0Z'
          fill='url(#paint0_linear_514_29887)'
        />
        <path
          d='M52.8042 50.2784C50.9778 44.5081 47.3924 39.6889 42.8196 35.3988C39.2746 32.0766 35.2646 29.24 31.258 26.3934C27.6927 23.8616 24.0972 21.3666 20.9094 18.4429C17.415 15.2346 14.982 11.6176 13.8733 7.49169V2.29071C13.87 1.02479 12.8388 0 11.565 0H11.5448H1.8163C1.69836 0.0502349 1.58042 0.103819 1.47259 0.160752C0.609928 0.495651 0 1.3195 0 2.29071V59.7025C0 60.9684 1.03115 61.9932 2.30492 61.9932H2.54417C2.54417 61.9932 2.56102 61.9932 2.56776 61.9932C2.66212 61.9932 2.75647 61.9932 2.84745 61.9932H11.5617C12.8354 61.9932 13.8666 60.9684 13.8666 59.7025V54.3072C14.4731 52.0534 15.4874 49.9 16.9567 47.8671C18.6618 45.5128 20.7746 43.4833 23.0458 41.5744C23.6355 41.0787 23.6288 40.7673 23.0425 40.3419C20.1175 38.2287 17.1858 36.1189 14.2609 34.0023C14.1227 33.9018 13.9946 33.8482 13.8666 33.8148V28.4664C16.2861 30.5093 18.8606 32.4048 21.4452 34.2903C25.2328 37.0532 29.0743 39.7693 32.6665 42.7264C38.257 47.3279 41.3538 53.0313 41.2695 59.967C41.2561 61.1559 42.1827 61.9496 43.5509 61.963C46.3646 61.9898 49.1784 61.9965 51.9955 61.9932C53.2962 61.9932 54.2195 61.1727 54.2263 60.0173C54.2465 56.7118 53.8084 53.4599 52.7975 50.275L52.8042 50.2784Z'
          fill='url(#paint1_linear_514_29887)'
        />
      </g>
      <defs>
        <linearGradient
          id='paint0_linear_514_29887'
          x1='42.0172'
          y1='-0.545885'
          x2='44.397'
          y2='50.2731'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#34315A' />
          <stop offset='1' stopColor='#3779E2' />
        </linearGradient>
        <linearGradient
          id='paint1_linear_514_29887'
          x1='0'
          y1='30.9983'
          x2='54.2297'
          y2='30.9983'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#34315A' />
          <stop offset='1' stopColor='#3779E2' />
        </linearGradient>
        <clipPath id='clip0_514_29887'>
          <rect width='54.2499' height='61.9999' fill='white' />
        </clipPath>
      </defs>
    </svg>
  ),
  logoLight: (props: IconProps) => (
    <svg
      width='296'
      height='335'
      viewBox='0 0 296 335'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <g clipPath='url(#clip0_440_32333)'>
        <path
          d='M286.283 0.84082C293.11 3.48863 295.252 8.1358 295.142 14.6202C294.355 61.5243 275.355 101.872 240.155 136.491C235.414 141.157 230.325 145.552 225.493 150.163C223.095 152.45 220.953 152.486 218.226 150.523C202.649 139.319 186.998 128.206 171.384 117.056C167.723 114.444 167.614 112.841 171.055 109.995C183.319 99.8184 194.705 88.957 203.893 76.4024C217.732 57.4895 224.303 36.6673 224.486 14.26C224.541 7.88363 226.756 3.36254 233.456 0.84082C251.065 0.84082 268.692 0.84082 286.301 0.84082L286.283 0.84082Z'
          fill='white'
        />
        <path
          d='M287.29 271.26C277.368 240.224 257.892 214.305 233.053 191.231C213.796 173.363 192.014 158.106 170.249 142.796C150.883 129.178 131.352 115.759 114.036 100.035C95.0537 82.7788 81.8377 63.3255 75.8155 41.1343V13.1612C75.7972 6.35259 70.1959 0.84082 63.2768 0.84082H63.1669H10.3213C9.68064 1.111 9.03998 1.3992 8.45423 1.70541C3.76823 3.50664 0.455078 7.93767 0.455078 13.1612V321.946C0.455078 328.755 6.05631 334.267 12.9755 334.267H14.2751C14.2751 334.267 14.3666 334.267 14.4032 334.267C14.9158 334.267 15.4283 334.267 15.9225 334.267H63.2585C70.1776 334.267 75.7789 328.755 75.7789 321.946V292.928C79.0737 280.806 84.5834 269.224 92.5643 258.291C101.826 245.628 113.303 234.713 125.641 224.446C128.844 221.78 128.808 220.105 125.623 217.817C109.734 206.451 93.809 195.104 77.9205 183.72C77.17 183.179 76.4744 182.891 75.7789 182.711V153.945C88.9216 164.933 102.906 175.128 116.946 185.269C137.521 200.129 158.388 214.737 177.901 230.642C208.268 255.391 225.09 286.066 224.633 323.369C224.559 329.763 229.593 334.032 237.025 334.104C252.309 334.249 267.594 334.285 282.896 334.267C289.962 334.267 294.977 329.854 295.014 323.639C295.124 305.861 292.744 288.371 287.253 271.242L287.29 271.26Z'
          fill='white'
        />
      </g>
      <defs>
        <clipPath id='clip0_440_32333'>
          <rect
            width='294.687'
            height='333.462'
            fill='white'
            transform='translate(0.455078 0.84082)'
          />
        </clipPath>
      </defs>
    </svg>
  ),
  google: (props: IconProps) => (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='23'
      height='22'
      viewBox='0 0 23 22'
      fill='none'
      {...props}
    >
      <path
        d='M20.1822 9.06454H19.4577V9.02722H11.3631V12.6248H16.4461C15.7045 14.7191 13.7119 16.2224 11.3631 16.2224C8.38297 16.2224 5.96673 13.8062 5.96673 10.826C5.96673 7.84586 8.38297 5.42962 11.3631 5.42962C12.7388 5.42962 13.9903 5.94858 14.9432 6.79626L17.4871 4.25231C15.8808 2.75526 13.7321 1.83203 11.3631 1.83203C6.3962 1.83203 2.36914 5.85909 2.36914 10.826C2.36914 15.7929 6.3962 19.82 11.3631 19.82C16.33 19.82 20.3571 15.7929 20.3571 10.826C20.3571 10.223 20.295 9.63431 20.1822 9.06454Z'
        fill='#FFC107'
      />
      <path
        d='M3.40625 6.63976L6.36122 8.80686C7.16079 6.82729 9.09719 5.42962 11.3632 5.42962C12.7389 5.42962 13.9904 5.94858 14.9433 6.79626L17.4872 4.25231C15.8809 2.75526 13.7322 1.83203 11.3632 1.83203C7.90864 1.83203 4.91274 3.78238 3.40625 6.63976Z'
        fill='#FF3D00'
      />
      <path
        d='M11.3624 19.8203C13.6855 19.8203 15.7964 18.9313 17.3924 17.4855L14.6087 15.13C13.6756 15.8401 12.535 16.224 11.3624 16.2227C9.02303 16.2227 7.03671 14.7311 6.28841 12.6494L3.35547 14.9092C4.84397 17.8219 7.86685 19.8203 11.3624 19.8203Z'
        fill='#4CAF50'
      />
      <path
        d='M20.1823 9.06467H19.4579V9.02734H11.3633V12.6249H16.4462C16.0915 13.6217 15.4526 14.4926 14.6083 15.1302L14.6097 15.1293L17.3933 17.4848C17.1963 17.6638 20.3573 15.3231 20.3573 10.8261C20.3573 10.2231 20.2952 9.63444 20.1823 9.06467Z'
        fill='#1976D2'
      />
    </svg>
  ),
  microsoft: (props: IconProps) => (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='22'
      height='22'
      viewBox='0 0 22 22'
      fill='none'
      {...props}
    >
      <g clipPath='url(#clip0_480_17033)'>
        <path
          d='M11.5684 10.1514H21.6866V0.707752C21.6866 0.52885 21.6155 0.357276 21.489 0.230774C21.3625 0.104271 21.1909 0.0332031 21.012 0.0332031L11.5684 0.0332031V10.1514Z'
          fill='#4CAF50'
        />
        <path
          d='M10.2198 10.1514V0.0332031H0.776111C0.59721 0.0332031 0.425636 0.104271 0.299133 0.230774C0.172631 0.357276 0.101563 0.52885 0.101562 0.707752L0.101563 10.1514H10.2198Z'
          fill='#F44336'
        />
        <path
          d='M10.2198 11.5H0.101562V20.9437C0.101563 21.1226 0.172631 21.2942 0.299133 21.4207C0.425636 21.5472 0.59721 21.6182 0.776111 21.6182H10.2198V11.5Z'
          fill='#2196F3'
        />
        <path
          d='M11.5684 11.5V21.6182H21.012C21.1909 21.6182 21.3625 21.5472 21.489 21.4207C21.6155 21.2942 21.6866 21.1226 21.6866 20.9437V11.5H11.5684Z'
          fill='#FFC107'
        />
      </g>
      <defs>
        <clipPath id='clip0_480_17033'>
          <rect
            width='21.5856'
            height='21.5856'
            fill='white'
            transform='translate(0.101562 0.0332031)'
          />
        </clipPath>
      </defs>
    </svg>
  ),
  linkedin: (props: IconProps) => <Linkedin {...props} />,
  instagram: (props: IconProps) => <Instagram {...props} />,
  facebook: (props: IconProps) => <Facebook {...props} />,
  x: (props: IconProps) => <X {...props} />,
  megaphone: (props: IconProps) => <Megaphone {...props} />,
  calendar: (props: IconProps) => <Calendar {...props} />,
  calendarInput: (props: IconProps) => <CalendarInput {...props} />,
  galleryHorizontal: (props: IconProps) => <GalleryHorizontal {...props} />,
  folderOpen: (props: IconProps) => <FolderOpen {...props} />,
  crown: (props: IconProps) => <Crown {...props} />,
  headset: (props: IconProps) => <Headset {...props} />,
  layoutDashboard: (props: IconProps) => <LayoutDashboard {...props} />,
  loginIcon: (props: IconProps) => <LoginIcon {...props} />,
  circlePlay: (props: IconProps) => <CirclePlay {...props} />,
  sparkles: (props: IconProps) => <Sparkles {...props} />,
  eye: (props: IconProps) => <Eye {...props} />,
  trash: (props: IconProps) => <Trash {...props} />,
  star: (props: IconProps) => <Star {...props} />,
  filter: (props: IconProps) => <Filter {...props} />,
  sort: (props: IconProps) => <Sort {...props} />,
  list: (props: IconProps) => <List {...props} />,
  listGrid: (props: IconProps) => <ListGrid {...props} />,
  emojiIcon: (props: IconProps) => <EmojiIcon {...props} />,
  hashtag: (props: IconProps) => <Hashtag {...props} />,
  magicStick: (props: IconProps) => <MagicStick {...props} />,
  swap: (props: IconProps) => <Swap {...props} />,
  bookmark: (props: IconProps) => <Bookmark {...props} />,
  cloudUpload: (props: IconProps) => <CloudUpload {...props} />,
  calendarAdd: (props: IconProps) => <CalendarAdd {...props} />,
  galleryEdit: (props: IconProps) => <GalleryEdit {...props} />,
  penNew: (props: IconProps) => <PenNew {...props} />,
  refreshSquare: (props: IconProps) => <RefreshSquare {...props} />,
  facebookChat: (props: IconProps) => <FacebookChat {...props} />,
  facebookLike: (props: IconProps) => <FacebookLike {...props} />,
  facebookShare: (props: IconProps) => <FacebookShare {...props} />,
  linkedinThumbsUp: (props: IconProps) => <LinkedinThumbsUp {...props} />,
  linkedinSend: (props: IconProps) => <LinkedinSend {...props} />,
  linkedinComment: (props: IconProps) => <LinkedinComment {...props} />,
  linkedinRepost: (props: IconProps) => <LinkedinRepost {...props} />,
  xReply: (props: IconProps) => <XReply {...props} />,
  xLike: (props: IconProps) => <XLike {...props} />,
  xRepost: (props: IconProps) => <XRepost {...props} />,
  xView: (props: IconProps) => <XView {...props} />,
  xShare: (props: IconProps) => <XShare {...props} />,
  xBookmark: (props: IconProps) => <XBookmark {...props} />,
};
