import { Skeleton } from '@/components/ui/skeleton';

export default function TableSkeleton() {
  return (
    <div className='w-full rounded-lg bg-background p-4'>
      {/* Table Header */}
      <div className='flex items-center justify-between pb-4'>
        <Skeleton className='h-10 w-1/3' />
        <Skeleton className='h-10 w-1/6' />
      </div>

      <div className='overflow-auto rounded-md border border-muted-foreground/80'>
        <table className='w-full border-collapse'>
          {/* Table Head */}
          <thead>
            <tr className='border-b border-muted-foreground text-left'>
              <th className='p-4'>
                <Skeleton className='h-6 w-20' />
              </th>
              <th className='p-4'>
                <Skeleton className='h-6 w-28' />
              </th>
              <th className='p-4'>
                <Skeleton className='h-6 w-24' />
              </th>
              <th className='p-4'>
                <Skeleton className='h-6 w-16' />
              </th>
              <th className='p-4'>
                <Skeleton className='h-6 w-20' />
              </th>
              <th className='p-4'>
                <Skeleton className='h-6 w-16' />
              </th>
            </tr>
          </thead>

          {/* Table Body */}
          <tbody>
            {Array.from({ length: 8 }).map((_, index) => (
              <tr key={index} className='border-b border-muted-foreground/80'>
                <td className='p-4'>
                  <Skeleton className='h-6 w-24' />
                </td>
                <td className='p-4'>
                  <Skeleton className='h-6 w-32' />
                </td>
                <td className='p-4'>
                  <Skeleton className='h-6 w-20' />
                </td>
                <td className='p-4'>
                  <Skeleton className='h-6 w-16 rounded-md' />
                </td>
                <td className='p-4'>
                  <Skeleton className='h-6 w-24' />
                </td>
                <td className='p-4'>
                  <Skeleton className='h-6 w-16' />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination Skeleton */}
      <div className='mt-4 flex items-center justify-between'>
        <Skeleton className='h-8 w-24' />
        <div className='flex gap-2'>
          <Skeleton className='h-8 w-8 rounded-md' />
          <Skeleton className='h-8 w-8 rounded-md' />
          <Skeleton className='h-8 w-8 rounded-md' />
        </div>
      </div>
    </div>
  );
}
