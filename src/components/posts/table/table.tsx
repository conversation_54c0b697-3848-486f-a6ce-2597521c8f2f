'use client';

import { format } from 'date-fns';
import { useTranslations } from 'next-intl';
import { useRef, useState } from 'react';
import { toast } from 'sonner';

import {
  Search,
  ChevronUp,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';

import { cn } from '@/lib/utils';

import {
  useDeletePrivateModel,
  useMarkPrivateModelAsFavorite,
} from '@/api/hooks/ai/mutations';
import { usePostsByCompanyBrandId } from '@/api/hooks/posts/queries';
import {
  flexRender,
  SortingState,
  useReactTable,
  PaginationState,
  VisibilityState,
  getCoreRowModel,
  getSortedRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
  getPaginationRowModel,
  getFacetedUniqueValues,
} from '@tanstack/react-table';

import { useColumns } from '@/components/posts/table/columns';
import { Icons } from '@/components/shared/icons';
import TableSkeleton from '@/components/shared/table-skeleton';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardHeader, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectItem,
  SelectValue,
  SelectContent,
  SelectTrigger,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableRow,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
} from '@/components/ui/table';

export function PostsTable() {
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [selectedDate, setSelectedDate] = useState<Date | undefined>();
  const [deletingId, setDeletingId] = useState<string | undefined>();

  const inputRef = useRef<HTMLInputElement>(null);

  const t = useTranslations();

  const {
    data: allPosts,
    refetch,
    isPending,
    isError,
    error,
    isRefetching,
  } = usePostsByCompanyBrandId();

  const { mutateAsync: handleDeleteClick } = useDeletePrivateModel();
  const { mutateAsync: handleStarClick } = useMarkPrivateModelAsFavorite();

  const columns = useColumns({
    t,
    onDelete: async (id: string) => {
      setDeletingId(id);
      const toastId = toast.loading('Deleting model...');

      try {
        await handleDeleteClick(id);

        toast.success('Post deleted', { id: toastId });
      } catch (error: unknown) {
        if (error instanceof Error) {
          toast.error('Deletion failed', { id: toastId });
        }
      } finally {
        await refetch();
        setDeletingId(undefined);
      }
    },
    onStar: async (id: string) => {
      try {
        await handleStarClick(id);
      } catch (error) {
        console.log(error);
      } finally {
        await refetch();
      }
    },
  });

  const table = useReactTable({
    data: allPosts ?? [],
    columns,
    state: {
      sorting,
      pagination,
      columnFilters,
      columnVisibility,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  });

  if (isError) {
    return <div>{error.message}</div>;
  }

  if (isPending) {
    return <TableSkeleton />;
  }

  return (
    <Card>
      <CardHeader className='px-6 py-4'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <div className='relative w-[300px]'>
              <Input
                placeholder='Search'
                className='border-none bg-[#F4F4F5] focus-visible:ring-0 focus-visible:ring-offset-0'
                ref={inputRef}
                value={
                  (table.getColumn('Ad.title')?.getFilterValue() as string) ??
                  ''
                }
                onChange={(e) =>
                  table.getColumn('Ad.title')?.setFilterValue(e.target.value)
                }
              />
              <Search
                className='absolute right-3 top-1/2 -translate-y-1/2 text-gray-400'
                size={18}
              />
            </div>
          </div>
          <div className='flex items-center gap-2'>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant='ghost'
                  className={cn(
                    'w-[240px] justify-between bg-[#F4F4F5] text-[#71717A]',
                    selectedDate && 'border-blue-200 bg-blue-50'
                  )}
                >
                  <span className='text-xs text-gray-500'>
                    {selectedDate
                      ? format(selectedDate, 'yyyy-MM-dd')
                      : 'Select a date'}
                  </span>
                  <Icons.calendarInput className='h-4 w-4' />
                </Button>
              </PopoverTrigger>
              <PopoverContent className='w-auto p-0' align='end'>
                <Calendar
                  mode='single'
                  selected={selectedDate}
                  onSelect={(date) => {
                    setSelectedDate(date);
                    table.getColumn('CreationTimestamp')?.setFilterValue(date);
                  }}
                  initialFocus
                />
                {selectedDate && (
                  <div className='border-t p-2'>
                    <Button
                      variant='ghost'
                      size='sm'
                      className='w-full'
                      onClick={() => {
                        setSelectedDate(undefined);
                        table
                          .getColumn('CreationTimestamp')
                          ?.setFilterValue(undefined);
                      }}
                    >
                      Clear
                    </Button>
                  </div>
                )}
              </PopoverContent>
            </Popover>
            <div className='flex items-center gap-2'>
              <button
                className={cn(
                  'rounded-md border-none px-1.5 py-1',
                  viewMode === 'list'
                    ? 'bg-[#D7E4F8] text-primary'
                    : 'bg-[#F4F4F5] text-[#71717A] hover:bg-[#D7E4F8] hover:text-primary'
                )}
                onClick={() => setViewMode('list')}
              >
                <Icons.list className='h-7 w-6' />
              </button>
              <button
                className={cn(
                  'rounded-md border-none px-2.5 py-2',
                  viewMode === 'grid'
                    ? 'bg-[#D7E4F8] text-primary'
                    : 'bg-[#F4F4F5] text-[#71717A] hover:bg-[#D7E4F8] hover:text-primary'
                )}
                onClick={() => setViewMode('grid')}
              >
                <Icons.listGrid className='h-5 w-4' />
              </button>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <div className='flex flex-1 flex-col'>
          <div
            className={cn('flex-1 overflow-hidden rounded-lg bg-background')}
          >
            {viewMode === 'list' ? (
              <ScrollArea type='always' className='h-full'>
                <Table>
                  <TableHeader>
                    {table.getHeaderGroups().map((headerGroup) => (
                      <TableRow key={headerGroup.id}>
                        {headerGroup.headers.map((header) => (
                          <TableHead
                            key={header.id}
                            style={{ width: header.getSize() }}
                          >
                            {header.isPlaceholder ? null : (
                              <div
                                className={cn(
                                  'flex items-center gap-2',
                                  header.column.getCanSort() && 'cursor-pointer'
                                )}
                                onClick={header.column.getToggleSortingHandler()}
                              >
                                {flexRender(
                                  header.column.columnDef.header,
                                  header.getContext()
                                )}
                                {{
                                  asc: <ChevronUp size={16} />,
                                  desc: <ChevronDown size={16} />,
                                }[header.column.getIsSorted() as string] ??
                                  null}
                              </div>
                            )}
                          </TableHead>
                        ))}
                      </TableRow>
                    ))}
                  </TableHeader>
                  <TableBody>
                    {table.getRowModel().rows.length === 0 ? (
                      <TableRow>
                        <TableCell
                          colSpan={columns.length}
                          className='h-24 text-center text-muted-foreground'
                        >
                          No data available
                        </TableCell>
                      </TableRow>
                    ) : (
                      table.getRowModel().rows.map((row) => (
                        <TableRow key={row.id}>
                          {row.getVisibleCells().map((cell) => (
                            <TableCell key={cell.id}>
                              {row.original.Id === deletingId ||
                              isRefetching ? (
                                <Skeleton
                                  style={{
                                    width: cell.column.getSize(),
                                  }}
                                  className='h-7'
                                />
                              ) : (
                                flexRender(
                                  cell.column.columnDef.cell,
                                  cell.getContext()
                                )
                              )}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </ScrollArea>
            ) : (
              <div className='grid grid-cols-1 gap-4 p-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'>
                {table.getRowModel().rows.length === 0 ? (
                  <div className='col-span-full flex h-24 items-center justify-center text-center text-muted-foreground'>
                    No data available
                  </div>
                ) : (
                  table.getRowModel().rows.map((row) => (
                    <div
                      key={row.id}
                      className='rounded-lg border bg-white p-4 transition-shadow hover:shadow-md'
                    >
                      <div className='left-1 flex flex-col gap-2'>
                        {row.getVisibleCells().map((cell) => (
                          <div
                            key={cell.id}
                            className='flex items-center justify-between'
                          >
                            <span className='text-sm text-gray-900'>
                              {row.original.Id === deletingId ||
                              isRefetching ? (
                                <Skeleton className='h-4 w-[100px]' />
                              ) : (
                                flexRender(
                                  cell.column.columnDef.cell,
                                  cell.getContext()
                                )
                              )}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))
                )}
              </div>
            )}
          </div>
          <div className='mt-4 flex items-center justify-between'>
            <div className='flex items-center gap-3'>
              <Select
                value={table.getState().pagination.pageSize.toString()}
                onValueChange={(v) => table.setPageSize(Number(v))}
              >
                <SelectTrigger className='w-[120px]'>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {[5, 10, 20].map((size) => (
                    <SelectItem key={size} value={size.toString()}>
                      Show {size}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className='flex items-center gap-2'>
              <Button
                variant='outline'
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                <ChevronLeft className='mr-2' size={16} />
                Previous
              </Button>
              <Button
                variant='outline'
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
              >
                Next
                <ChevronRight className='ml-2' size={16} />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
