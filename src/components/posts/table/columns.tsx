'use client';

import type { FilterFn, ColumnDef } from '@tanstack/react-table';

import { format, isSameDay } from 'date-fns';
import { useCallback } from 'react';

import { PostSuggestion } from '@/api/models/dtos/post.dto';

import { cn } from '@/lib/utils';

import { Icons } from '@/components/shared/icons';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

export const usePostsColumns = () => {
  const typeFilterFn: FilterFn<PostSuggestion> = useCallback(
    (row, columnId, filterValue: string[]) => {
      if (!filterValue?.length) return true;
      const type = row.getValue<string>(columnId);
      return filterValue.includes(type);
    },
    []
  );

  const postColumnsDef: ColumnDef<PostSuggestion>[] = [
    {
      header: 'Name',
      accessorKey: 'Ad.title',
      size: 200,
      enableHiding: false,
    },
    {
      header: 'Status',
      accessorKey: 'Status',
      size: 100,
    },
    {
      header: 'Social media',
      accessorKey: 'SocialMedia',
      size: 100,
    },
    {
      header: 'Type',
      accessorKey: 'AdType',
      cell: ({ row }) => (
        <Badge
          className={cn(
            'rounded-md font-normal lbg-[#d7e4f8]',
          )}
        >
          {row.getValue('AdType')}
        </Badge>
      ),
      size: 100,
      filterFn: typeFilterFn,
    },
    {
      header: 'Created by',
      accessorKey: 'UserEmail',
      size: 180,
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const isFavorite = row.original.Favorite;

        return (
          <div className='flex gap-2'>
            <Button
              variant='ghost'
              size='setting'
              className='text-gray-400 hover:text-primary'
            >
              <Icons.eye />
            </Button>

            <Button
              variant='ghost'
              size='setting'
              className={cn({
                'text-muted-foreground hover:text-[#F5A524]': !isFavorite,
                'text-[#F5A524] hover:text-muted-foreground': isFavorite,
              })}
            >
              <Icons.star fill={isFavorite ? 'currentColor' : 'white'} />
            </Button>

            <Button
              variant='ghost'
              size='icon'
              className='size-8 text-muted-foreground hover:text-destructive'
            >
              <Icons.trash />
            </Button>
          </div>
        );
      },
      size: 60,
      enableHiding: false,
    },
  ];

  return { postColumnsDef };
};
