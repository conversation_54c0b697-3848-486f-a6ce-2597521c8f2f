'use client';

import { useEffect } from 'react';

import { useDirection } from '@/hooks/use-direction';

import { usePostsByCompanyBrandId } from '@/api/hooks/posts/queries';
import { redirect } from '@/i18n/routing';
import { useCurrentUserStore } from '@/stores/current-user-store';

import { usePostsColumns } from '@/components/posts/table/columns';
import { DataTable } from '@/components/shared/table/data-table';

export default function PageContent() {
  const { locale } = useDirection();
  const {
    data: allPosts,
    isLoading,
    isError,
    error,
  } = usePostsByCompanyBrandId();

  const { postColumnsDef } = usePostsColumns();

  const { getUser } = useCurrentUserStore();
  const currentUser = getUser();

  useEffect(() => {
    if (!currentUser) {
      redirect({ locale, href: '/login' });
    }
  }, [locale, currentUser]);

  return (
    <div className='flex h-full flex-col'>
      <div className='flex items-center justify-between'>
        <h1 className='text-2xl font-bold'>Posts list</h1>
      </div>

      <p className='text-gray-0 my-2'>Here is the list of posts created</p>

      <DataTable
        columns={postColumnsDef}
        data={allPosts ?? []}
        isLoading={isLoading}
        isError={isError}
        error={error}
      />
    </div>
  );
}
