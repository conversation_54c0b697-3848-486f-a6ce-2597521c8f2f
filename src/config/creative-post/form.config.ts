import {
  UpdatePostSchema,
  GenerateNewPostSuggestionsSchema,
} from '@/api/models/schemas/creative.schema';
import { formOptions } from '@tanstack/react-form';

export const creativePostFormOpts = formOptions({
  defaultValues: {
    GenerationType: 'text_image',
    CompanyId: '',
    BrandId: '',
    UserEmail: '',
    Context: '',
    Project: '',
    SocialMedia: 'facebook',
    MarketingStrategy: '',
    ImageList: [],
    CallToAction: {
      CTAtype: '',
      CTALink: '',
      WithCallToAction: false,
    },
    TextOptions: {
      Language: 'english',
      ToneOfVoice: '',
      Knowledges: null,
      OriginalityLevel: 5,
      WithHashtag: false,
      WithEmoji: false,
      PrivateModelId: '',
    },
    ImageOptions: {
      PrivateModelId: '',
      ColorKnowledgeId: [],
      Keywords: [],
      ImageStyle: '',
      ImageType: '',
      ImageEffects: [],
      ImageRatio: 'custom',
      ImageWidth: 1200,
      ImageHeight: 1200,
      NumberOfImages: 1,
      DetailsLevel: 5,
    },
  } as GenerateNewPostSuggestionsSchema,
});

export const updatePostPromptFormOpts = formOptions({
  defaultValues: {
    textprompt: '',
  },
});

export const updatePostFormOpts = formOptions({
  defaultValues: {
    Id: '',
    CreaFormId: '',
    CompanyId: '',
    BrandId: '',
    UserEmail: '',
    SocialMedia: 'facebook',
    Project: '',
    Context: '',
    CallToAction: {
      CTAtype: '',
      CTALink: '',
      WithCallToAction: false,
    },
    Ad: {
      title: '',
      message: {
        Text: '',
        URL: '',
      },
      language: '',
    },
    MarketingStrategy: '',
    TextOptions: {
      Language: 'english',
      ToneOfVoice: '',
      Knowledges: null,
      OriginalityLevel: 5,
      WithHashtag: false,
      WithEmoji: false,
      PrivateModelId: '',
    },
  } as UpdatePostSchema,
});
