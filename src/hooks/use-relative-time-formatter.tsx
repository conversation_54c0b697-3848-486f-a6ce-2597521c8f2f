import { parseISO, formatDistanceToNowStrict } from 'date-fns';
import { useTranslations } from 'next-intl';

import { useDateFnsLocale } from '@/hooks/use-date-fns-locale';

export function useRelativeTimeFormatter() {
  const locale = useDateFnsLocale();
  const t = useTranslations();

  return (timestamp: string) => {
    const date = parseISO(timestamp);

    const diffMs = new Date().getTime() - date.getTime();
    const diffMinutes = diffMs / (1000 * 60);

    if (diffMinutes < 1) {
      return t('time_ago.just_now');
    } else if (diffMinutes < 60) {
      return formatDistanceToNowStrict(date, {
        addSuffix: true,
        locale: locale,
      });
    } else {
      return formatDistanceToNowStrict(date, {
        addSuffix: true,
        locale: locale,
      });
    }
  };
}
