import { CampaignResponse } from '@/api/models/dtos/compaign.dto';

import { apiGet } from '@/api/config/client';
import { ENDPOINTS } from '@/api/config/endpoints';

export const campaignService = {
  async getAllByCompanyBrandId(companyId?: string, brandId?: string) {
    if (!companyId || !brandId) {
      throw new Error('companyId and brandId are required');
    }

    return await apiGet<CampaignResponse[]>({
      endpoint: ENDPOINTS.CAMPAIGNS.ALL_BY_COMPANY_BRAND_ID(companyId, brandId),
    });
  },
};
