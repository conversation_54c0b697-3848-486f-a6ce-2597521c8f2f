import { apiGet, apiPost, apiDelete } from '../config/client';
import { ENDPOINTS } from '../config/endpoints';
import {
  CheckLimitResponse,
  PrivateModelResponse,
  PrepareImageModelResponse,
  PrepareTextPrivateModelResponse,
} from '../models/dtos/ai-model.dto';
import {
  SaveTextModelSchema,
  SaveImageModelSchema,
  PrepareTextModelSchema,
  PrepareImageModelSchema,
} from '../models/schemas/private-model.schema';

export const aiService = {
  async getAllByCompanyBrandId(companyId?: string, brandId?: string) {
    if (!companyId || !brandId) {
      throw new Error('companyId and brandId are required');
    }

    return await apiGet<PrivateModelResponse[]>({
      endpoint: ENDPOINTS.AI_MODELS.ALL_BY_COMPANY_BRAND_ID(companyId, brandId),
    });
  },

  async getPrivateModelDetail(id: string) {
    return await apiGet<PrivateModelResponse>({
      endpoint: ENDPOINTS.AI_MODELS.BY_ID(id),
    });
  },

  async prepareTextPrivateModel({
    platformName,
    pageName,
  }: {
    platformName?: PrepareTextModelSchema['SocialMediaData'][number]['platform'];
    pageName?: string;
  }) {
    if (!platformName || !pageName) {
      throw new Error('platformName and pageName are required');
    }

    return await apiGet<PrepareTextPrivateModelResponse[]>({
      endpoint: ENDPOINTS.AI_MODELS.PREPARE_TEXT_PRIVATE_MODEL(platformName),
      config: {
        baseURL: process.env.BACKEND_NEXT_SERVER_URL,
        timeout: 120000,
        params: {
          page: pageName,
        },
      },
    });
  },

  async checkLimit(companyId?: string) {
    return await apiPost<CheckLimitResponse>({
      endpoint: ENDPOINTS.AI_MODELS.CHECK_LIMIT,
      payload: {
        companyId,
      },
    });
  },

  async createLLMTextModel(payload: SaveTextModelSchema) {
    return await apiPost({
      endpoint: ENDPOINTS.AI_MODELS.CREATE_TEXT_PRIVATE_MODEL,
      payload,
    });
  },

  async createDummyLLMTextModel(payload: SaveTextModelSchema) {
    return await apiPost({
      endpoint: ENDPOINTS.AI_MODELS.CREATE_DUMMY_TEXT_PRIVATE_MODEL,
      payload,
    });
  },

  async prepareImagePrivateModel(payload: PrepareImageModelSchema) {
    const formData = new FormData();

    const { files, ...resPayload } = payload;

    formData.append('modelRequest', JSON.stringify(resPayload.modelRequest));

    files.forEach((file) => {
      formData.append('file', file);
    });

    return await apiPost<PrepareImageModelResponse>({
      endpoint: ENDPOINTS.AI_MODELS.PREPARE_IMAGE_PRIVATE_MODEL,
      payload: formData,
      config: {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    });
  },

  async createImagePrivateModel(payload: SaveImageModelSchema) {
    return await apiPost<PrivateModelResponse>({
      endpoint: ENDPOINTS.AI_MODELS.CREATE_IMAGE_PRIVATE_MODEL,
      payload,
    });
  },

  async markPrivateModelAsFavorite(privateModelId?: string) {
    if (!privateModelId) {
      throw new Error('privateModelId is required');
    }

    return await apiPost<PrivateModelResponse>({
      endpoint: ENDPOINTS.AI_MODELS.MARK_FAVORITE(privateModelId),
    });
  },

  async deletePrivateModel(privateModelId?: string) {
    if (!privateModelId) {
      throw new Error('privateModelId is required');
    }

    return await apiDelete<PrivateModelResponse>({
      endpoint: ENDPOINTS.AI_MODELS.DELETE(privateModelId),
    });
  },
};
