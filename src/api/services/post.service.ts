import {
  PromptType,
  PostSuggestion,
  PostsSuggestionsResponse,
} from '@/api/models/dtos/post.dto';

import { apiGet, apiPost } from '@/api/config/client';
import { ENDPOINTS } from '@/api/config/endpoints';
import {
  UpdatePostSchema,
  UpdatePostPromptSchema,
  GenerateNewPostSuggestionsSchema,
} from '@/api/models/schemas/creative.schema';

export const postService = {
  async generateNewPostSuggestions(
    creativeData: Omit<GenerateNewPostSuggestionsSchema, 'ImageList'>
  ) {
    return await apiPost<PostsSuggestionsResponse>({
      endpoint: ENDPOINTS.POSTS.CREATE_NEW_SUGGESTIONS,
      payload: creativeData,
    });
  },

  async saveSuggestionsById(postId?: string) {
    if (!postId) {
      throw new Error('postId is required');
    }

    return await apiPost({
      endpoint: ENDPOINTS.POSTS.SAVE_SUGGESTIONS_BY_ID(postId),
    });
  },

  async getPostById(postId: string) {
    return await apiGet({
      endpoint: ENDPOINTS.POSTS.BY_ID(postId),
    });
  },

  async getSavedSuggestionsById(postId: string) {
    return await apiGet({
      endpoint: ENDPOINTS.POSTS.SAVED_SUGGESTIONS_BY_ID(postId),
    });
  },

  async getPostsByCompanyBrandId(companyId?: string, brandId?: string) {
    if (!companyId || !brandId) {
      throw new Error('companyId and brandId are required');
    }

    return await apiGet<PostSuggestion[]>({
      endpoint: ENDPOINTS.POSTS.BY_COMPANY_BRAND_ID(companyId, brandId),
    });
  },

  async getGeneratedPostPrompt(type: PromptType, adContentId?: string) {
    if (!adContentId) {
      throw new Error('adContentId are required');
    }

    return await apiGet<PostSuggestion['Ad']>({
      endpoint: ENDPOINTS.POSTS.GET_GENERATED_POST_PROMPT,
      queryParams: {
        type,
        adContentId,
      },
    });
  },

  async updatePostPrompt(
    type: string,
    adContentId: string,
    payload: UpdatePostPromptSchema
  ) {
    return await apiPost<PostSuggestion>({
      endpoint: ENDPOINTS.POSTS.UPDATE_GENERATED_POST_PROMPT,
      queryParams: {
        type,
        adContentId,
      },
      payload,
    });
  },

  async updatePost(payload: UpdatePostSchema) {
    return await apiPost<PostSuggestion>({
      endpoint: ENDPOINTS.POSTS.UPDATE,
      payload,
    });
  },

  async markPostAsFavorite(postId: string) {
    return await apiPost({
      endpoint: ENDPOINTS.POSTS.MARK_FAVORITE(postId),
    });
  },

  async deletePost(postId: string) {
    return await apiPost({
      endpoint: ENDPOINTS.POSTS.DELETE(postId),
    });
  },
};
