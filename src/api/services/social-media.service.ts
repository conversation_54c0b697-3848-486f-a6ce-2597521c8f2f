import { SocialMediaInsightsResponse } from '@/api/models/dtos/social_media_insights.dto';

import { apiPost } from '@/api/config/client';
import { ENDPOINTS } from '@/api/config/endpoints';

export const socialMediaService = {
  async getFacebookInsights(BrandId?: string, companyId?: string) {
    if (!BrandId) {
      throw new Error('BrandId is required');
    }

    if (!companyId) {
      throw new Error('companyId is required');
    }

    return apiPost<SocialMediaInsightsResponse>({
      endpoint: ENDPOINTS.SOCIAL_MEDIA.INSIGHTS_BY_PLATFORM('facebook'),
      payload: {
        BrandId,
        companyId,
      },
    });
  },

  async getInstagramInsights(BrandId?: string, companyId?: string) {
    if (!BrandId) {
      throw new Error('BrandId is required');
    }

    if (!companyId) {
      throw new Error('companyId is required');
    }

    return apiPost<SocialMediaInsightsResponse>({
      endpoint: ENDPOINTS.SOCIAL_MEDIA.INSIGHTS_BY_PLATFORM('instagram'),
      payload: {
        BrandId,
        companyId,
      },
    });
  },

  async getLinkedinInsights(BrandId?: string, companyId?: string) {
    if (!BrandId) {
      throw new Error('BrandId is required');
    }

    if (!companyId) {
      throw new Error('companyId is required');
    }

    return apiPost<SocialMediaInsightsResponse>({
      endpoint: ENDPOINTS.SOCIAL_MEDIA.INSIGHTS_BY_PLATFORM('linkedin'),
      payload: {
        BrandId,
        companyId,
      },
    });
  },
};
