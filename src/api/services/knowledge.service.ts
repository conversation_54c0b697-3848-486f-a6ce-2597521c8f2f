import { KnowledgeResponse } from '@/api/models/dtos/knowledge.dto';

import { apiGet } from '@/api/config/client';
import { ENDPOINTS } from '@/api/config/endpoints';

export const knowledgeService = {
  async getAllByCompanyBrandId(companyId?: string, brandId?: string) {
    if (!companyId || !brandId) {
      throw new Error('companyId and brandId are required');
    }

    return await apiGet<KnowledgeResponse[]>({
      endpoint: ENDPOINTS.KNOWLEDGES.ALL_BY_COMPANY_BRAND_ID(
        companyId,
        brandId
      ),
    });
  },
};
