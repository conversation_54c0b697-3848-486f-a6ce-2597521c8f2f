import { useTranslations } from 'next-intl';
import { z } from 'zod';

const CallToActionSchema = z.object({
  CTAtype: z.string().optional(),
  CTALink: z.string().optional(),
  WithCallToAction: z.boolean(),
});

const TextOptionsSchema = z.object({
  Language: z.string().optional(),
  ToneOfVoice: z.string().optional(),
  Knowledges: z.array(z.string()).nullable(),
  PrivateModelId: z.string().optional(),
  OriginalityLevel: z.number().int().min(0).max(10),
  WithHashtag: z.boolean(),
  WithEmoji: z.boolean(),
});

const ImageOptionsSchema = z.object({
  PrivateModelId: z.string().optional(),
  ImageUplaod: z.string().optional(),
  FileUploadExtension: z.string().optional(),
  ColorKnowledgeId: z.array(z.string()).optional(),
  Keywords: z.array(z.string()).optional(),
  ImageStyle: z.string().optional(),
  ImageType: z.string().optional(),
  ImageEffects: z.array(z.string()).optional(),
  ImageRatio: z.string(),
  ImageWidth: z.number().int().positive(),
  ImageHeight: z.number().int().positive(),
  NumberOfImages: z.number().int().positive().optional(),
  DetailsLevel: z.number().int().min(1).max(10).optional(),
});

export const generateNewPostSuggestionsSchema = (
  t: ReturnType<typeof useTranslations>
) =>
  z.object({
    CompanyId: z.string().nonempty(t('messages_schema.company_id_required')),
    BrandId: z.string().nonempty(t('messages_schema.brand_id_required')),
    UserEmail: z
      .string()
      .email()
      .nonempty(t('messages_schema.user_email_required')),
    GenerationType: z.enum([
      'text_only',
      'text_for_image',
      'text_for_pdf',
      'text_image',
      'text_many_image',
    ]),
    SocialMedia: z.enum(['facebook', 'instagram', 'linkedin', 'x']),
    Context: z.string().min(1, t('messages_schema.title_required')),
    Project: z
      .string()
      .min(1, { message: t('messages_schema.description_required') }),
    MarketingStrategy: z.string(),
    CallToAction: CallToActionSchema.optional(),
    TextOptions: TextOptionsSchema.optional(),
    ImageOptions: ImageOptionsSchema,
    ImageList: z
      .array(z.custom<File>())
      .max(1, t('messages_schema.up_to_one_files'))
      .optional()
      .refine((files) => files?.every((file) => file.size <= 5 * 1024 * 1024), {
        message: t('messages_schema.file_size'),
        path: ['files'],
      }),
  });

export type GenerateNewPostSuggestionsSchema = z.infer<
  ReturnType<typeof generateNewPostSuggestionsSchema>
>;

export const updatePostPromptSchema = (t: ReturnType<typeof useTranslations>) =>
  z.object({
    textprompt: z.string().min(1, t('messages_schema.text_prompt_required')),
  });

export type UpdatePostPromptSchema = z.infer<
  ReturnType<typeof updatePostPromptSchema>
>;

export const updatePostSchema = (t: ReturnType<typeof useTranslations>) =>
  z.object({
    Id: z.string(),
    CreaFormId: z.string(),
    ...generateNewPostSuggestionsSchema(t).partial().pick({
      CompanyId: true,
      BrandId: true,
      UserEmail: true,
      SocialMedia: true,
      Project: true,
      Context: true,
      CallToAction: true,
      MarketingStrategy: true,
      TextOptions: true,
    }).shape,
    AdType: z.enum([
      'text_only',
      'text_for_image',
      'text_for_pdf',
      'text_image',
      'text_many_image',
    ]),
    Ad: z.object({
      title: z.string().min(1, t('messages_schema.title_required')),
      message: z.object({
        Text: z.string().min(1, t('messages_schema.description_required')),
        URL: z.string().optional(),
      }),
      language: z.string().optional(),
    }),
  });

export type UpdatePostSchema = z.infer<ReturnType<typeof updatePostSchema>>;
