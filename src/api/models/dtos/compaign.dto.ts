export interface CampaignResponse {
  Id: string;
  Name: string;
  CompanyId: string;
  BrandId: string;
  UserEmail: string;
  Description: string;
  Objective: string;
  StartDate: Date;
  EndDate: Date;
  Platforms: null;
  PlateformsPageIds: null;
  Knowledges: string[];
  Audiences: null;
  CampaignType: string;
  AICampaignFromId: string;
  Proposal: null;
  PostsId: string[];
  Posts: Post[];
  Favorite: boolean;
  LastModifTimestamp: Date;
  CreationTimestamp: Date;
  FbInsights: Insights;
  InstaInsights: Insights;
  LinkedinInsights: Insights;
  XInsights: Insights;
}

export interface Insights {
  EngagedUsers: number;
  Engagement: number;
  Impression: number;
  Reach: number;
  EngagementPercentage: number;
  CTAPercentage: number;
  Reaction: number;
  Click: number;
  LikeTotal: number;
  Comment: number;
  Share: number;
  Saved: number;
  Like: number;
  Love: number;
  Wow: number;
  Haha: number;
  Sorry: number;
  Anger: number;
  Retweet: number;
  Reply: number;
  Quote: number;
  UpdateTime: Date;
  Platform: string;
}

export interface Post {
  Id: string;
  CompanyId: string;
  BrandId: string;
  CampaignId: string;
  ContentId: string;
  Content: Content;
  ProposalId: string;
  ScheduleInfos: null;
  InstantInfos: null;
  LastModifTimestamp: Date;
  CreationTimestamp: Date;
}

export interface Content {
  Id: string;
  CreaFormId: string;
  UserEmail: string;
  CompanyId: string;
  BrandId: string;
  Ad: Ad;
  AdType: string;
  Status: string;
  SocialMedia: string;
  Medias: Media[];
  Saved: boolean;
  Favorite: boolean;
  Score: Score;
  LastModifTimestamp: Date;
  CreationTimestamp: Date;
}

export interface Ad {
  title: string;
  message: Message;
  imagesprompts: Imagesprompt[];
  language: string;
  textprompt: string;
}

export interface Imagesprompt {
  index: number;
  prompt: string;
}

export interface Message {
  Text: string;
  URL: string;
}

export interface Media {
  Index: number;
  GcsLink: string;
  GcsLinkPublic: string;
  Width: number;
  Height: number;
  MediaType: string;
}

export interface Score {
  Id: string;
  ContentId: string;
  FormId: string;
  CompanyId: string;
  BrandId: string;
  UserEmail: string;
  PopularityScore: string;
  AwarenessScore: string;
  ConsiderationScore: string;
  ConversionScore: string;
  LoyaltyScore: string;
  Explanation: string;
  LastModifTimestamp: Date;
  CreationTimestamp: Date;
}
