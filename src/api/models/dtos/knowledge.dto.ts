export interface KnowledgeResponse {
  Id: string;
  UserEmail: string;
  CompanyId: string;
  BrandId: string;
  Type: string;
  Name: string;
  Text: Text;
  Audience: Audience;
  Color: Color[] | null;
  Font: Font;
  Logo: Logo;
  Website: Website;
  Doc: Doc;
  File: string;
  FileName: string;
  LastModifTimestamp: Date;
  CreationTimestamp: Date;
  Favorite: boolean;
}

export interface Audience {
  Name: string;
  AgeMin: number;
  AgeMax: number;
  Gender: string;
  InterestAndPref: null;
  SpendingBehavior: null;
  Business: boolean;
  BusinessIndustry: null;
}

export interface Color {
  Index: number;
  ColorHex: string;
}

export interface Doc {
  Title: string;
  Type: string;
  Description: string;
  GcsLink: string;
  GcsLinkPublic: string;
}

export interface Font {
  Name: string;
  Family: string;
  Size: number;
  Bold: boolean;
  GcsLink: string;
  GcsLinkPublic: string;
}

export interface Logo {
  Name: string;
  GcsLink: string;
  GcsLinkPublic: string;
}

export interface Text {
  Title: string;
  Description: string;
  Labels: null;
}

export interface Website {
  Title: string;
  Link: string;
  Description: string;
}
