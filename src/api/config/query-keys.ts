import { PromptType } from '@/api/models/dtos/post.dto';

import { PrepareTextModelSchema } from '@/api/models/schemas/private-model.schema';

export const QUERY_KEYS = {
  USERS: {
    BY_EMAIL: (email?: string) => ['users', email],
  },
  BRANDS: {
    ALL_BY_COMPANY_ID: (companyId?: string) => ['brands', companyId],
    DETAIL: (brandId?: string) => ['brand', brandId],
  },
  SOCIAL_MEDIA: {
    INSIGHTS_BY_PLATFORM: (brandId?: string, companyId?: string) => [
      'allInsights',
      brandId,
      companyId,
    ],
  },
  POSTS: {
    ALL: ['posts'],
    BY_COMPANY_BRAND_ID: (companyId?: string, brandId?: string) => [
      'posts',
      companyId,
      brandId,
    ],
    DETAIL: (postId: string) => ['post', postId],
    SAVE_SUGGESTIONS_BY_ID: (postId: string) => ['saveSuggestions', postId],
    GET_GENERATED_POST_PROMPT: (type: PromptType, adContentId?: string) => [
      'generatedPostPrompt',
      type,
      adContentId,
    ],
  },
  PRIVATE_MODELS: {
    All: ['privateModels'],
    ALL_BY_COMPANY_BRAND_ID: (companyId?: string, brandId?: string) => [
      'privateModels',
      companyId,
      brandId,
    ],
    PREPARE_TEXT_PRIVATE_MODEL: (
      platformName?: PrepareTextModelSchema['SocialMediaData'][number]['platform'],
      pageName?: string
    ) => ['privateModelPreviewData', platformName, pageName],
    DETAIL: (id: string) => ['privateModelDetail', id],
  },
  KNOWLEDGES: {
    ALL_BY_COMPANY_BRAND_ID: (companyId?: string, brandId?: string) => [
      'knowledges',
      companyId,
      brandId,
    ],
  },
  CAMPAIGNS: {
    ALL_BY_COMPANY_BRAND_ID: (companyId?: string, brandId?: string) => [
      'campaigns',
      companyId,
      brandId,
    ],
  },
  // Add more query keys as needed
};
