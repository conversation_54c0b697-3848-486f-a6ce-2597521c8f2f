import axios, { AxiosError, AxiosInstance, AxiosRequestConfig } from 'axios';

const DEFAULT_TIMEOUT = 30000;
const API_URL = process.env.BACKEND_SERVER_URL;

export const createAxiosInstance = (
  config?: AxiosRequestConfig
): AxiosInstance => {
  const axiosInstance = axios.create({
    baseURL: API_URL,
    timeout: DEFAULT_TIMEOUT,
    headers: {
      'Content-Type': 'application/json',
    },
    ...config,
  });

  axiosInstance.interceptors.request.use(
    (config) => {
      const token =
        typeof window !== 'undefined'
          ? localStorage.getItem('auth_token')
          : null;

      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor - Handle common errors, refresh tokens, etc.
  axiosInstance.interceptors.response.use(
    (response) => response,
    async (error: AxiosError) => {
      const originalRequest = error.config;

      if (
        error.code === 'ECONNABORTED' ||
        error.message.includes('Network Error')
      ) {
        return Promise.reject(new Error('server_error'));
      }

      // Handle 401 Unauthorized - refresh token flow
      if (error.response?.status === 401 && originalRequest) {
        // Implement token refresh logic here
        // Example:
        // try {
        //   const newToken = await refreshToken();
        //   originalRequest.headers.Authorization = `Bearer ${newToken}`;
        //   return axiosInstance(originalRequest);
        // } catch (refreshError) {
        //   // Handle refresh token failure (logout users, etc.)
        // }
      }

      return Promise.reject(error);
    }
  );

  return axiosInstance;
};

export const axiosInstance = createAxiosInstance();
