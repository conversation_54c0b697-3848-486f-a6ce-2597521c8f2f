import { PlatformType } from '@/api/models/dtos/social_media_insights.dto';

import { PrepareTextModelSchema } from '@/api/models/schemas/private-model.schema';

export const ENDPOINTS = {
  AUTH: {
    LOGIN: 'auth/login',
    REGISTER: 'auth/register',
    LOGOUT: 'auth/logout',
  },
  USERS: {
    BY_EMAIL: (email: string) => `user/${email}`,
  },
  BRANDS: {
    ALL_BY_COMPANY_ID: (companyId: string) => `brands/${companyId}`,
    BY_ID: (brandId: string) => `brand/${brandId}`,
  },
  SOCIAL_MEDIA: {
    INSIGHTS_BY_PLATFORM: (platform: PlatformType) => `${platform}/getinsights`,
  },
  POSTS: {
    CREATE_NEW_SUGGESTIONS: 'form/create',
    SAVE_SUGGESTIONS_BY_ID: (postId: string) => `content/save/${postId}`,
    BY_ID: (postId: string) => `content/${postId}`,
    SAVED_SUGGESTIONS_BY_ID: (postId: string) => `content/saved/${postId}`,
    BY_COMPANY_BRAND_ID: (companyId: string, brandId: string) =>
      `contents/company/${companyId}/${brandId}`,
    GET_GENERATED_POST_PROMPT: 'form/gen_prompt',
    UPDATE_GENERATED_POST_PROMPT: 'form/process_prompt',
    UPDATE: 'content/update',
    MARK_FAVORITE: (postId: string) => `content/favorite/${postId}`,
    DELETE: (postId: string) => `content/delete/${postId}`,
  },
  AI_MODELS: {
    ALL_BY_COMPANY_BRAND_ID: (companyId: string, brandId: string) =>
      `ai/models/company/${companyId}/${brandId}`,
    BY_ID: (id: string) => `ai/model/${id}`,
    CHECK_LIMIT: 'ai/models/checklimit',
    PREPARE_TEXT_PRIVATE_MODEL: (
      platformName?: PrepareTextModelSchema['SocialMediaData'][number]['platform']
    ) => `social-media-scraper/${platformName}`,
    CREATE_TEXT_PRIVATE_MODEL: 'ai/model/create/llm',
    CREATE_DUMMY_TEXT_PRIVATE_MODEL: 'dummy/ai/model/create/llm',
    PREPARE_IMAGE_PRIVATE_MODEL: 'ai/model/preparedata/image',
    CREATE_IMAGE_PRIVATE_MODEL: 'ai/model/create/image',
    MARK_FAVORITE: (id: string) => `ai/models/favorite/${id}`,
    DELETE: (id: string) => `ai/model/delete/${id}`,
  },
  KNOWLEDGES: {
    ALL_BY_COMPANY_BRAND_ID: (companyId: string, brandId: string) =>
      `knowledges/${companyId}/${brandId}`,
  },
  CAMPAIGNS: {
    ALL_BY_COMPANY_BRAND_ID: (companyId: string, brandId: string) =>
      `campaign/company/${companyId}/${brandId}`,
  },
  // Add more endpoints as needed
};
