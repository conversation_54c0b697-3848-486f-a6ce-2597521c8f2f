import { QUERY_KEYS } from '@/api/config/query-keys';
import { knowledgeService } from '@/api/services/knowledge.service';
import { useCurrentUserStore } from '@/stores/current-user-store';
import { useQuery } from '@tanstack/react-query';

export const useAllKnowledge = () => {
  const { getUser } = useCurrentUserStore();
  const user = getUser();

  return useQuery({
    queryKey: QUERY_KEYS.KNOWLEDGES.ALL_BY_COMPANY_BRAND_ID(
      user?.companyId,
      user?.brandId
    ),
    queryFn: async () =>
      await knowledgeService.getAllByCompanyBrandId(
        user?.companyId,
        user?.brandId
      ),
    enabled: !!user?.companyId && !!user?.brandId,
  });
};
