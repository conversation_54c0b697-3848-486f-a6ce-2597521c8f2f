import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

import { PromptType } from '@/api/models/dtos/post.dto';

import { QUERY_KEYS } from '@/api/config/query-keys';
import {
  UpdatePostSchema,
  UpdatePostPromptSchema,
  GenerateNewPostSuggestionsSchema,
} from '@/api/models/schemas/creative.schema';
import { postService } from '@/api/services/post.service';
import { useCreativePostStore } from '@/stores/creative-post-store';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export const useCreateNewPostSuggestions = () => {
  const t = useTranslations();

  const { setPostSuggestions, setCurrentPostSuggestion, getPostSuggestions } =
    useCreativePostStore();

  const setIsGeneratingNewPostSuggestions = useCreativePostStore(
    (s) => s.setIsGeneratingNewPostSuggestions
  );

  const clearIsGeneratingNewPostSuggestions = useCreativePostStore(
    (s) => s.clearIsGeneratingNewPostSuggestions
  );

  return useMutation({
    mutationFn: (
      payload: Omit<GenerateNewPostSuggestionsSchema, 'ImageList'>
    ) => postService.generateNewPostSuggestions(payload),
    onMutate: () => {
      setIsGeneratingNewPostSuggestions(true);
    },
    onSuccess: (data) => {
      clearIsGeneratingNewPostSuggestions();
      const currentPostSuggestions = getPostSuggestions();

      const mergedResults = currentPostSuggestions
        ? [...currentPostSuggestions.results, ...data?.results]
        : data?.results;

      setPostSuggestions({
        ...data,
        results: mergedResults,
      });

      // Set the current post-suggestion to the second item in the result array
      // cause the carousel displays the second item as the first one
      setCurrentPostSuggestion(data?.results[1]);

      toast.success(t('toast.success.generate_post'));
    },
    onError: ({ message }) => {
      toast.error(message);
    },
  });
};

export const useSaveSuggestionsById = () => {
  const t = useTranslations();
  const queryClient = useQueryClient();

  const selectedIndex = useCreativePostStore((s) => s.selectedIndex);
  const postSuggestions = useCreativePostStore((s) => s.postSuggestions);

  const setSelectedIndex = useCreativePostStore((s) => s.setSelectedIndex);
  const setPostSuggestions = useCreativePostStore((s) => s.setPostSuggestions);

  const resetSelectedIndex = useCreativePostStore((s) => s.resetSelectedIndex);
  const clearPostSuggestions = useCreativePostStore(
    (s) => s.clearPostSuggestions
  );
  const clearCurrentPostSuggestion = useCreativePostStore(
    (s) => s.clearCurrentPostSuggestion
  );

  return useMutation({
    mutationFn: async ({ postId }: { postId: string }) =>
      await postService.saveSuggestionsById(postId),
    onSuccess: async () => {
      if (!postSuggestions) return;

      const filteredResults = postSuggestions.results.filter(
        (_, index) => index !== selectedIndex
      );

      setPostSuggestions({
        ...postSuggestions,
        results: filteredResults,
      });

      if (filteredResults.length > 0) {
        const newIndex = Math.min(
          selectedIndex > 0 ? selectedIndex - 1 : 0,
          filteredResults.length - 1
        );
        setSelectedIndex(newIndex);
      } else {
        clearPostSuggestions();
        clearCurrentPostSuggestion();
        resetSelectedIndex();
      }

      toast.success(t('toast.success.save_post'));

      await queryClient.invalidateQueries({ queryKey: QUERY_KEYS.POSTS.ALL });
    },
  });
};

export const useUpdatePostPrompt = () => {
  const t = useTranslations();

  const queryClient = useQueryClient();
  const { setEditPostSuggestion } = useCreativePostStore();

  return useMutation({
    mutationFn: async ({
      type,
      adContentId,
      payload,
    }: {
      type: PromptType;
      adContentId: string;
      payload: UpdatePostPromptSchema;
    }) => await postService.updatePostPrompt(type, adContentId, payload),
    onSuccess: async (data, variables) => {
      setEditPostSuggestion(data);

      toast.success(t('toast.success.update_post'));
      await queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.POSTS.GET_GENERATED_POST_PROMPT(
          variables.type,
          variables.adContentId
        ),
      });
    },
    onError: ({ message }) => {
      toast.error(message);
    },
  });
};

export const useUpdatePost = () => {
  const t = useTranslations();
  const { currentPostSuggestion, setEditPostSuggestion } =
    useCreativePostStore();

  return useMutation({
    mutationFn: async ({ payload }: { payload: UpdatePostSchema }) =>
      await postService.updatePost(payload),
    onSuccess: async (data) => {
      setEditPostSuggestion({
        ...data,
        Medias: currentPostSuggestion?.Medias ?? null,
      });

      toast.success(t('toast.success.update_post'));
    },
    onError: ({ message }) => {
      toast.error(message);
    },
  });
};
