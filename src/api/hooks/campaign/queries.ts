import { QUERY_KEYS } from '@/api/config/query-keys';
import { campaignService } from '@/api/services/campaign.service';
import { useCurrentUserStore } from '@/stores/current-user-store';
import { useQuery } from '@tanstack/react-query';

export const useAllCampaigns = () => {
  const { getUser } = useCurrentUserStore();
  const user = getUser();

  return useQuery({
    queryKey: QUERY_KEYS.CAMPAIGNS.ALL_BY_COMPANY_BRAND_ID(
      user?.companyId,
      user?.brandId
    ),
    queryFn: async () =>
      await campaignService.getAllByCompanyBrandId(
        user?.companyId,
        user?.brandId
      ),
    enabled: !!user?.companyId && !!user?.brandId,
  });
};
