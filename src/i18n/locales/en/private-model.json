{"name": "Name", "description": "Description", "creation_Date": "Creation Date", "type": "Type", "created_by": "Created By", "actions": "Actions", "title_private_model_list": "Private Model List", "subtitle_private_model_list": "A private model captures the style and characteristics of inputs as a reference for AI-generated posts", "create_a_private_model": "Create a private model", "search": "Search", "filter": "Filter", "model_type": "Model Type", "text": "Text", "image": "Image", "url": "Url", "clear_all": "Clear all", "apply": "Apply", "sort": "Sort", "clear_sorting": "Clear sorting", "select_a_date": "Select a date", "clear": "Clear", "show": "Show", "previous": "Previous", "next": "Next", "cancel": "Cancel", "private_model_form": {"model_name": "Model Name", "model_name_placeholder": "Content", "description": "Description", "description_placeholder": "Content", "topics": "Topics", "add": "Add", "social_media": "Social Media", "add_social_media": "Add Social Media", "select_platform": "Select Platform", "page_name": "Page Name", "image_category": "Image Category", "image_category_placeholder": "Select Image Category", "keyword": "Keyword", "object_name": "Object Name", "image_urls": "Image Urls", "url": "Url", "import_image": "Import Image", "drag_drop": "Drag and drop or", "link": "choose files", "upload": "to upload", "delete": "Delete", "new_model": "New Model", "info_general": "Info General", "preview": "Preview", "text_model": "Text Model", "image_model": "Image Model", "save": "Save", "cancel": "Cancel"}, "preview_form": {"close": "Close"}, "text-schema": {"topic-error": "Please enter at least one topic", "model-name-error": "Please enter a model name", "model-description-error": "Please enter a model description", "platform-error": "Platform must be one of 'facebook', 'instagram', 'linkedin', or 'x'", "pageName-error": "Page name is required"}, "toast": {"deleting_model": "Deleting model...", "model_deleted": "Model deleted", "deletion_failed": "Deletion failed", "model_marked_as_favorite": "Model marked as favorite", "model_marked_as_not_favorite": "Model marked as not favorite", "error_deleting_model": "Error deleting model", "model_created_successfully": "Your Model created successfully", "model_prapared_successfully": "Your Model prapared successfully", "error_preparing_model": "Error preparing model"}, "details": {"error_loading_model": "Error loading model", "private_model_details": "Private Model Details", "creation_date": "Creation Date:", "created_by": "Created By:", "model_status": "Model Status:", "tags": "Tags:", "no_tags": "No tags", "images": "Images", "texts": "Description"}}