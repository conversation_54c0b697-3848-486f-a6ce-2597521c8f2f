import type { Metada<PERSON> } from 'next';

import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { <PERSON><PERSON><PERSON> } from 'next/font/google';
import React from 'react';
import { isRtlLang, getLangDir } from 'rtl-detect';

import { Locale } from '@/i18n/routing';
import ReactQueryProvider from '@/providers/react-query-provider';
import '@/styles/globals.css';
import { SessionProviderWrapper } from '@/providers/session-provider';
import { ThemeProvider } from '@/providers/theme-provider';

import { ReactScan } from '@/components/development/react-scan';
import { Toaster } from '@/components/ui/sonner';
import { TooltipProvider } from '@/components/ui/tooltip';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'Kwore | Home',
  description: 'Welcome to Kwore',
  icons: {
    icon: [
      {
        media: '(prefers-color-scheme: dark)',
        url: '/favicon/favicon-light.svg',
      },
      {
        media: '(prefers-color-scheme: light)',
        url: '/favicon/favicon-dark.svg',
      },
    ],
  },
};

interface RootLayoutProps {
  children: React.ReactNode;
  params: Promise<{ locale: Locale }>;
}

export default async function RootLayout({
  children,
  params,
}: Readonly<RootLayoutProps>) {
  const { locale } = await params;

  const messages = await getMessages();
  const direction = getLangDir(locale);
  const isRtl = isRtlLang(locale);

  return (
    <html lang={locale} dir={direction} suppressHydrationWarning>
      <body className={`${geistSans.className} antialiased`}>
        {process.env.NODE_ENV === 'development' ? <ReactScan /> : null}
        <NextIntlClientProvider locale={locale} messages={messages}>
          <SessionProviderWrapper>
            <ReactQueryProvider>
              <ThemeProvider
                attribute='class'
                defaultTheme='light'
                enableSystem
              >
                <TooltipProvider>{children}</TooltipProvider>
                <Toaster
                  richColors={true}
                  position={isRtl ? 'bottom-left' : 'bottom-right'}
                />
              </ThemeProvider>
            </ReactQueryProvider>
          </SessionProviderWrapper>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
