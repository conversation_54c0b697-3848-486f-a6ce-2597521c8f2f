import React from 'react';

import { CampaignsTable } from '@/components/campaigns/campaigns-table';
import { ScrollArea } from '@/components/ui/scroll-area';

export default async function Page() {
  await new Promise((resolve) => setTimeout(resolve, 1000));

  return (
    <div className='flex h-full'>
      <ScrollArea className='flex-1'>
        <main className='flex h-full flex-1 flex-col gap-4 overflow-auto p-4'>
          <CampaignsTable />
        </main>
      </ScrollArea>
    </div>
  );
}
