declare namespace NodeJS {
  interface ProcessEnv {
    NEXT_PUBLIC_TESTING: string;

    NEXT_AUTH_SECRET: string;
    NEXT_AUTH_URL: string;

    GOOGLE_CLIENT_ID: string;
    GOOGLE_CLIENT_SECRET: string;

    LINKEDIN_CLIENT_ID: string;
    LINKEDIN_CLIENT_SECRET: string;

    MICROSOFT_CLIENT_ID: string;
    MICROSOFT_CLIENT_SECRET: string;
    MICROSOFT_TENANT_ID: string;

    JWT_SECRET: string;
    BACKEND_SERVER_URL: string;
    BACKEND_NEXT_SERVER_URL: string;

    NEXT_PUBLIC_FIREBASE_API_KEY: string;
    NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: string;
    NEXT_PUBLIC_FIREBASE_PROJECT_ID: string;
    NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: string;
    NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: string;
    NEXT_PUBLIC_FIREBASE_APP_ID: string;

    FIREBASE_PROJECT_ID: string;
    FIREBASE_CLIENT_EMAIL: string;
    FIREBASE_PRIVATE_KEY: string;

    APIFY_API_TOKEN: string;
    MAX_SCRAP_POST: string;

    // Add other environment variables here...
  }
}
